import 'package:flutter/services.dart';
import '../models/ai_response.dart';

class AiService {
  static const MethodChannel _channel = MethodChannel('com.example.myapp/llm');

  String buildPrompt({
    required String userInput,
    required double latitude,
    required double longitude,
    bool hasImage = false,
  }) {
    return '''
You are an emergency assistant. Given the following situation:

$userInput

Location: $latitude, $longitude

${hasImage ? "if You are also provided with an image of the situation. Analyze the image and use it as additional context to improve your response get a small needed details like weight, height, gender, ergency and this small things. ." : ""}

1. Write a concise SMS message that the user can send to emergency services or a contact, describing the situation and location make it as small as posible don't add any fancy contacts or thing just give the situation spotted points and the location only .
2. Provide a step-by-step guide for what the user should do next, you have to know that 95% the user will be in panic and he is inexperienced so make the steps small and on point of exactly what to do .

Format your response as:
SMS:
<the sms message here>

GUIDE:
1. Step one
2. Step two
...''';
  }

  Map<String, dynamic> parseModelOutput(String output) {
    // Extract SMS and GUIDE from model output
    final smsReg = RegExp(r'SMS:\s*(.*?)\nGUIDE:', dotAll: true);
    final guideReg = RegExp(r'GUIDE:\s*(.*)', dotAll: true);
    String sms = '';
    List<String> guideSteps = [];
    final smsMatch = smsReg.firstMatch(output);
    if (smsMatch != null) {
      sms = smsMatch.group(1)?.trim() ?? '';
    }
    final guideMatch = guideReg.firstMatch(output);
    if (guideMatch != null) {
      final guideRaw = guideMatch.group(1) ?? '';
      guideSteps = guideRaw
          .split(RegExp(r'\n\d+\.'))
          .map((s) => s.trim())
          .where((s) => s.isNotEmpty)
          .toList();
    }
    return {'sms': sms, 'guideSteps': guideSteps};
  }

  Future<void> loadModel() async {
    print('[AiService] Calling native initModel');
    final result = await _channel.invokeMethod('initModel');
    print('[AiService] Native initModel result: $result');
  }

  Future<AiResponse> runInference(
    String prompt, {
    String? imagePath,
    String? audioPath,
    double? latitude,
    double? longitude,
  }) async {
    print('[AiService] Preparing to call native runLlmInference');
    print('[AiService] prompt: $prompt');
    print('[AiService] imagePath: $imagePath');
    print('[AiService] audioPath: $audioPath');
    print('[AiService] latitude: $latitude, longitude: $longitude');
    final args = <String, dynamic>{};
    // Build the real prompt
    final realPrompt = buildPrompt(
      userInput: prompt,
      latitude: latitude ?? 0.0,
      longitude: longitude ?? 0.0,
      hasImage: imagePath != null && imagePath.isNotEmpty,
    );
    args['text'] = realPrompt;
    if (imagePath != null && imagePath.isNotEmpty) {
      args['imagePath'] = imagePath;
    }
    if (audioPath != null && audioPath.isNotEmpty) {
      args['audioPath'] = audioPath;
    }
    print('[AiService] Args sent to native: $args');
    final response = await _channel.invokeMethod('runLlmInference', args);
    print('[AiService] Native runLlmInference response: $response');
    final output = response as String;
    final parsed = parseModelOutput(output);
    print('[AiService] Parsed SMS: ${parsed['sms']}');
    print('[AiService] Parsed guideSteps: ${parsed['guideSteps']}');
    return AiResponse(
      smsDraft: parsed['sms'] ?? '',
      guidanceSteps: List<String>.from(parsed['guideSteps'] ?? []),
    );
  }
}
