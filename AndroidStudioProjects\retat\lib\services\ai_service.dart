import 'package:flutter/services.dart';
import '../models/ai_response.dart';

class AiService {
  static const MethodChannel _channel = MethodChannel('com.example.myapp/llm');

  String buildPrompt({
    required String userInput,
    required double latitude,
    required double longitude,
    bool hasImage = false,
  }) {
    return '''$userInput
Lat: $latitude, Lng: $longitude
${hasImage ? "See image." : ""}

SMS:
GUIDE:''';
  }

  Map<String, dynamic> parseModelOutput(String output) {
    // Extract SMS and GUIDE from model output
    String sms = '';
    List<String> guideSteps = [];

    // Split by SMS: and GUIDE: markers
    final parts = output.split(RegExp(r'(SMS:|GUIDE:)', caseSensitive: false));

    if (parts.length >= 3) {
      // SMS content is after "SMS:" marker
      sms = parts[2].split(RegExp(r'GUIDE:', caseSensitive: false))[0].trim();

      // GUIDE content is after "GUIDE:" marker
      if (parts.length >= 4) {
        final guideContent = parts[3].trim();
        // Split by numbered steps
        guideSteps = guideContent
            .split(RegExp(r'\n\d+\.|\n-|\n\*'))
            .map((s) => s.trim())
            .where((s) => s.isNotEmpty && s.length > 2)
            .toList();
      }
    }

    // Fallback: try to extract any meaningful content
    if (sms.isEmpty && output.isNotEmpty) {
      final lines =
          output.split('\n').where((line) => line.trim().isNotEmpty).toList();
      if (lines.isNotEmpty) {
        sms = lines.first.trim();
        if (lines.length > 1) {
          guideSteps = lines
              .skip(1)
              .map((s) => s.trim())
              .where((s) => s.isNotEmpty)
              .toList();
        }
      }
    }

    return {'sms': sms, 'guideSteps': guideSteps};
  }

  Future<void> loadModel() async {
    print('[AiService] Calling native initModel');
    final result = await _channel.invokeMethod('initModel');
    print('[AiService] Native initModel result: $result');
  }

  Future<void> resetSession() async {
    print('[AiService] Calling native resetSession');
    final result = await _channel.invokeMethod('resetSession');
    print('[AiService] Native resetSession result: $result');
  }

  Future<AiResponse> runInference(
    String prompt, {
    String? imagePath,
    String? audioPath,
    double? latitude,
    double? longitude,
  }) async {
    print('[AiService] Preparing to call native runLlmInference');
    print('[AiService] prompt: $prompt');
    print('[AiService] imagePath: $imagePath');
    print('[AiService] audioPath: $audioPath');
    print('[AiService] latitude: $latitude, longitude: $longitude');

    // Build the real prompt
    final realPrompt = buildPrompt(
      userInput: prompt,
      latitude: latitude ?? 0.0,
      longitude: longitude ?? 0.0,
      hasImage: imagePath != null && imagePath.isNotEmpty,
    );

    final args = <String, dynamic>{
      'text': realPrompt,
    };

    if (imagePath != null && imagePath.isNotEmpty) {
      args['imagePath'] = imagePath;
    }
    if (audioPath != null && audioPath.isNotEmpty) {
      args['audioPath'] = audioPath;
    }

    print('[AiService] Args sent to native: $args');

    try {
      final response = await _channel.invokeMethod('runLlmInference', args);
      print('[AiService] Native runLlmInference response: $response');
      final output = response as String;

      // Check if we got an empty response (token limit exceeded)
      if (output.trim().isEmpty) {
        print(
            '[AiService] Empty response detected, resetting session and retrying...');
        await resetSession();

        // Retry with the same arguments
        final retryResponse =
            await _channel.invokeMethod('runLlmInference', args);
        print('[AiService] Retry response: $retryResponse');
        final retryOutput = retryResponse as String;

        final parsed = parseModelOutput(retryOutput);
        print('[AiService] Parsed SMS: ${parsed['sms']}');
        print('[AiService] Parsed guideSteps: ${parsed['guideSteps']}');
        return AiResponse(
          smsDraft: parsed['sms'] ?? '',
          guidanceSteps: List<String>.from(parsed['guideSteps'] ?? []),
        );
      }

      final parsed = parseModelOutput(output);
      print('[AiService] Parsed SMS: ${parsed['sms']}');
      print('[AiService] Parsed guideSteps: ${parsed['guideSteps']}');
      return AiResponse(
        smsDraft: parsed['sms'] ?? '',
        guidanceSteps: List<String>.from(parsed['guideSteps'] ?? []),
      );
    } catch (e) {
      print('[AiService] Error during inference: $e');
      // If there's an error, try resetting session and retry once
      try {
        print('[AiService] Attempting session reset due to error...');
        await resetSession();
        final retryResponse =
            await _channel.invokeMethod('runLlmInference', args);
        final retryOutput = retryResponse as String;
        final parsed = parseModelOutput(retryOutput);
        return AiResponse(
          smsDraft: parsed['sms'] ?? '',
          guidanceSteps: List<String>.from(parsed['guideSteps'] ?? []),
        );
      } catch (retryError) {
        print('[AiService] Retry also failed: $retryError');
        rethrow;
      }
    }
  }
}
