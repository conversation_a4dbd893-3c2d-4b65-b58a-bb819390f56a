package com.example.ai_comp_wearableos.services

import android.content.Context
import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.android.gms.wearable.*
import kotlinx.coroutines.*
import kotlinx.coroutines.tasks.await
import java.io.File

class PhoneCommunicationService(private val context: Context) {
    
    private val dataClient: DataClient = Wearable.getDataClient(context)
    private val messageClient: MessageClient = Wearable.getMessageClient(context)
    private val nodeClient: NodeClient = Wearable.getNodeClient(context)
    
    companion object {
        private const val TAG = "PhoneCommunication"
        private const val EMERGENCY_VOICE_PATH = "/emergency_voice"
        private const val SMS_RESPONSE_PATH = "/sms_response"
        private const val REQUEST_SMS_PATH = "/request_sms"
        private const val CONNECTION_CHECK_PATH = "/connection_check"
    }
    
    // Callback for receiving SMS responses
    var onSmsReceived: ((String) -> Unit)? = null
    var onConnectionStatusChanged: ((Boolean) -> Unit)? = null
    var onError: ((String) -> Unit)? = null
    
    private val dataListener = DataClient.OnDataChangedListener { dataEvents ->
        for (event in dataEvents) {
            if (event.type == DataEvent.TYPE_CHANGED) {
                val dataItem = event.dataItem
                when (dataItem.uri.path) {
                    SMS_RESPONSE_PATH -> {
                        handleSmsResponse(dataItem)
                    }
                }
            }
        }
    }
    
    private val messageListener = MessageClient.OnMessageReceivedListener { messageEvent ->
        when (messageEvent.path) {
            CONNECTION_CHECK_PATH -> {
                Log.d(TAG, "Connection check received from phone")
                onConnectionStatusChanged?.invoke(true)
            }
        }
    }
    
    fun startListening() {
        Log.d(TAG, "Starting to listen for phone communication")
        dataClient.addListener(dataListener)
        messageClient.addListener(messageListener)
    }
    
    fun stopListening() {
        Log.d(TAG, "Stopping phone communication listeners")
        dataClient.removeListener(dataListener)
        messageClient.removeListener(messageListener)
    }
    
    suspend fun sendVoiceToPhone(audioFile: File): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Sending voice file to phone: ${audioFile.absolutePath}")
                
                // Check if phone is connected
                val connectedNodes = nodeClient.connectedNodes.await()
                if (connectedNodes.isEmpty()) {
                    Log.e(TAG, "No connected phone found")
                    onError?.invoke("Phone not connected")
                    return@withContext false
                }
                
                // Create asset from audio file
                val asset = Asset.createFromBytes(audioFile.readBytes())

                // Create data request
                val putDataRequest = PutDataRequest.create(EMERGENCY_VOICE_PATH).apply {
                    val dataMap = DataMap().apply {
                        putAsset("audio_file", asset)
                        putLong("timestamp", System.currentTimeMillis())
                    }
                    setData(dataMap.toByteArray())
                    setUrgent() // High priority for emergency
                }
                
                // Send to phone
                val result = dataClient.putDataItem(putDataRequest).await()
                Log.d(TAG, "Voice sent to phone successfully: ${result.uri}")
                
                true
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send voice to phone", e)
                onError?.invoke("Failed to send voice: ${e.message}")
                false
            }
        }
    }
    
    suspend fun checkPhoneConnection(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val connectedNodes = nodeClient.connectedNodes.await()
                val isConnected = connectedNodes.isNotEmpty()
                
                if (isConnected) {
                    // Send ping to phone
                    for (node in connectedNodes) {
                        messageClient.sendMessage(
                            node.id,
                            CONNECTION_CHECK_PATH,
                            "ping".toByteArray()
                        ).await()
                    }
                    Log.d(TAG, "Phone connection verified: ${connectedNodes.size} nodes")
                } else {
                    Log.w(TAG, "No phone connection found")
                }
                
                onConnectionStatusChanged?.invoke(isConnected)
                isConnected
            } catch (e: Exception) {
                Log.e(TAG, "Failed to check phone connection", e)
                onConnectionStatusChanged?.invoke(false)
                false
            }
        }
    }
    
    private fun handleSmsResponse(dataItem: DataItem) {
        try {
            val dataMap = DataMapItem.fromDataItem(dataItem).dataMap
            val smsText = dataMap.getString("sms_text")
            val success = dataMap.getBoolean("success", false)
            
            if (success && !smsText.isNullOrEmpty()) {
                Log.d(TAG, "SMS response received: $smsText")
                onSmsReceived?.invoke(smsText)
            } else {
                val error = dataMap.getString("error", "Unknown error")
                Log.e(TAG, "SMS generation failed: $error")
                onError?.invoke("SMS generation failed: $error")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse SMS response", e)
            onError?.invoke("Failed to parse response: ${e.message}")
        }
    }
    
    suspend fun requestEmergencySms(voiceText: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val connectedNodes = nodeClient.connectedNodes.await()
                if (connectedNodes.isEmpty()) {
                    onError?.invoke("Phone not connected")
                    return@withContext false
                }
                
                // Send text-based request as fallback
                val putDataRequest = PutDataRequest.create(REQUEST_SMS_PATH).apply {
                    val dataMap = DataMap().apply {
                        putString("voice_text", voiceText)
                        putLong("timestamp", System.currentTimeMillis())
                    }
                    setData(dataMap.toByteArray())
                    setUrgent()
                }
                
                dataClient.putDataItem(putDataRequest).await()
                Log.d(TAG, "Emergency SMS request sent: $voiceText")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Failed to request emergency SMS", e)
                onError?.invoke("Failed to request SMS: ${e.message}")
                false
            }
        }
    }
}
