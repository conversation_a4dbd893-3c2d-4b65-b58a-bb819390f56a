  Manifest android  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  ActivityCompat android.app.Activity  Any android.app.Activity  
BitmapFactory android.app.Activity  Bundle android.app.Activity  	ByteArray android.app.Activity  Context android.app.Activity  	Exception android.app.Activity  	Executors android.app.Activity  GeneratedPluginRegistrant android.app.Activity  GraphOptions android.app.Activity  Handler android.app.Activity  Int android.app.Activity  LlmInference android.app.Activity  LlmInferenceSession android.app.Activity  Location android.app.Activity  LocationListener android.app.Activity  LocationManager android.app.Activity  Log android.app.Activity  Looper android.app.Activity  Manifest android.app.Activity  Map android.app.Activity  
MethodChannel android.app.Activity  Model android.app.Activity  NoSuchMethodException android.app.Activity  PackageManager android.app.Activity  
Recognizer android.app.Activity  Regex android.app.Activity  String android.app.Activity  
StringBuilder android.app.Activity  System android.app.Activity  also android.app.Activity  android android.app.Activity  com android.app.Activity  copyTo android.app.Activity  emptyMap android.app.Activity  get android.app.Activity  getSystemService android.app.Activity  inputStream android.app.Activity  
isNotEmpty android.app.Activity  
isNullOrBlank android.app.Activity  java android.app.Activity  	javaClass android.app.Activity  listOf android.app.Activity  locationManager android.app.Activity  mapOf android.app.Activity  outputStream android.app.Activity  	readBytes android.app.Activity  synchronized android.app.Activity  to android.app.Activity  use android.app.Activity  Context android.content  ActivityCompat android.content.Context  Any android.content.Context  
BitmapFactory android.content.Context  Bundle android.content.Context  	ByteArray android.content.Context  Context android.content.Context  	Exception android.content.Context  	Executors android.content.Context  GeneratedPluginRegistrant android.content.Context  GraphOptions android.content.Context  Handler android.content.Context  Int android.content.Context  LOCATION_SERVICE android.content.Context  LlmInference android.content.Context  LlmInferenceSession android.content.Context  Location android.content.Context  LocationListener android.content.Context  LocationManager android.content.Context  Log android.content.Context  Looper android.content.Context  Manifest android.content.Context  Map android.content.Context  
MethodChannel android.content.Context  Model android.content.Context  NoSuchMethodException android.content.Context  PackageManager android.content.Context  
Recognizer android.content.Context  Regex android.content.Context  String android.content.Context  
StringBuilder android.content.Context  System android.content.Context  also android.content.Context  android android.content.Context  assets android.content.Context  cacheDir android.content.Context  com android.content.Context  copyTo android.content.Context  emptyMap android.content.Context  filesDir android.content.Context  get android.content.Context  getExternalFilesDir android.content.Context  getSystemService android.content.Context  inputStream android.content.Context  
isNotEmpty android.content.Context  
isNullOrBlank android.content.Context  java android.content.Context  	javaClass android.content.Context  listOf android.content.Context  locationManager android.content.Context  mapOf android.content.Context  outputStream android.content.Context  	readBytes android.content.Context  synchronized android.content.Context  to android.content.Context  use android.content.Context  ActivityCompat android.content.ContextWrapper  Any android.content.ContextWrapper  
BitmapFactory android.content.ContextWrapper  Bundle android.content.ContextWrapper  	ByteArray android.content.ContextWrapper  Context android.content.ContextWrapper  	Exception android.content.ContextWrapper  	Executors android.content.ContextWrapper  GeneratedPluginRegistrant android.content.ContextWrapper  GraphOptions android.content.ContextWrapper  Handler android.content.ContextWrapper  Int android.content.ContextWrapper  LlmInference android.content.ContextWrapper  LlmInferenceSession android.content.ContextWrapper  Location android.content.ContextWrapper  LocationListener android.content.ContextWrapper  LocationManager android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  Manifest android.content.ContextWrapper  Map android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  Model android.content.ContextWrapper  NoSuchMethodException android.content.ContextWrapper  PackageManager android.content.ContextWrapper  
Recognizer android.content.ContextWrapper  Regex android.content.ContextWrapper  String android.content.ContextWrapper  
StringBuilder android.content.ContextWrapper  System android.content.ContextWrapper  also android.content.ContextWrapper  android android.content.ContextWrapper  com android.content.ContextWrapper  copyTo android.content.ContextWrapper  emptyMap android.content.ContextWrapper  get android.content.ContextWrapper  inputStream android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrBlank android.content.ContextWrapper  java android.content.ContextWrapper  	javaClass android.content.ContextWrapper  listOf android.content.ContextWrapper  locationManager android.content.ContextWrapper  mapOf android.content.ContextWrapper  outputStream android.content.ContextWrapper  	readBytes android.content.ContextWrapper  synchronized android.content.ContextWrapper  to android.content.ContextWrapper  use android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  AssetManager android.content.res  list  android.content.res.AssetManager  open  android.content.res.AssetManager  
BitmapFactory android.graphics  
decodeFile android.graphics.BitmapFactory  Location android.location  LocationListener android.location  LocationManager android.location  accuracy android.location.Location  latitude android.location.Location  	longitude android.location.Location  GPS_PROVIDER  android.location.LocationManager  NETWORK_PROVIDER  android.location.LocationManager  PASSIVE_PROVIDER  android.location.LocationManager  getLastKnownLocation  android.location.LocationManager  isProviderEnabled  android.location.LocationManager  
removeUpdates  android.location.LocationManager  requestLocationUpdates  android.location.LocationManager  Bundle 
android.os  Handler 
android.os  Looper 
android.os  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  Log android.util  d android.util.Log  ActivityCompat  android.view.ContextThemeWrapper  Any  android.view.ContextThemeWrapper  
BitmapFactory  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  	ByteArray  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  	Executors  android.view.ContextThemeWrapper  GeneratedPluginRegistrant  android.view.ContextThemeWrapper  GraphOptions  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  LlmInference  android.view.ContextThemeWrapper  LlmInferenceSession  android.view.ContextThemeWrapper  Location  android.view.ContextThemeWrapper  LocationListener  android.view.ContextThemeWrapper  LocationManager  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Looper  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  Map  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  Model  android.view.ContextThemeWrapper  NoSuchMethodException  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  
Recognizer  android.view.ContextThemeWrapper  Regex  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  
StringBuilder  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  also  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  com  android.view.ContextThemeWrapper  copyTo  android.view.ContextThemeWrapper  emptyMap  android.view.ContextThemeWrapper  get  android.view.ContextThemeWrapper  inputStream  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isNullOrBlank  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  	javaClass  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  locationManager  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  outputStream  android.view.ContextThemeWrapper  	readBytes  android.view.ContextThemeWrapper  synchronized  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  use  android.view.ContextThemeWrapper  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ActivityCompat com.example.myapp  Any com.example.myapp  
BitmapFactory com.example.myapp  Boolean com.example.myapp  Bundle com.example.myapp  	ByteArray com.example.myapp  Context com.example.myapp  	Exception com.example.myapp  	Executors com.example.myapp  FlutterActivity com.example.myapp  
FlutterEngine com.example.myapp  GeneratedPluginRegistrant com.example.myapp  GraphOptions com.example.myapp  Handler com.example.myapp  Int com.example.myapp  LlmInference com.example.myapp  LlmInferenceSession com.example.myapp  Location com.example.myapp  LocationListener com.example.myapp  LocationManager com.example.myapp  Log com.example.myapp  Looper com.example.myapp  MainActivity com.example.myapp  Manifest com.example.myapp  Map com.example.myapp  
MethodChannel com.example.myapp  Model com.example.myapp  NoSuchMethodException com.example.myapp  PackageManager com.example.myapp  
Recognizer com.example.myapp  Regex com.example.myapp  String com.example.myapp  
StringBuilder com.example.myapp  System com.example.myapp  also com.example.myapp  android com.example.myapp  com com.example.myapp  copyTo com.example.myapp  emptyMap com.example.myapp  get com.example.myapp  inputStream com.example.myapp  
isNotEmpty com.example.myapp  
isNullOrBlank com.example.myapp  java com.example.myapp  	javaClass com.example.myapp  listOf com.example.myapp  locationManager com.example.myapp  mapOf com.example.myapp  outputStream com.example.myapp  	readBytes com.example.myapp  synchronized com.example.myapp  to com.example.myapp  use com.example.myapp  ActivityCompat com.example.myapp.MainActivity  Any com.example.myapp.MainActivity  
BitmapFactory com.example.myapp.MainActivity  	ByteArray com.example.myapp.MainActivity  CHANNEL com.example.myapp.MainActivity  Context com.example.myapp.MainActivity  	Executors com.example.myapp.MainActivity  GeneratedPluginRegistrant com.example.myapp.MainActivity  GraphOptions com.example.myapp.MainActivity  Handler com.example.myapp.MainActivity  LlmInference com.example.myapp.MainActivity  LlmInferenceSession com.example.myapp.MainActivity  LocationManager com.example.myapp.MainActivity  Log com.example.myapp.MainActivity  Looper com.example.myapp.MainActivity  Manifest com.example.myapp.MainActivity  
MethodChannel com.example.myapp.MainActivity  Model com.example.myapp.MainActivity  PackageManager com.example.myapp.MainActivity  
Recognizer com.example.myapp.MainActivity  Regex com.example.myapp.MainActivity  
StringBuilder com.example.myapp.MainActivity  System com.example.myapp.MainActivity  also com.example.myapp.MainActivity  android com.example.myapp.MainActivity  com com.example.myapp.MainActivity  copyAssetFolder com.example.myapp.MainActivity  copyTo com.example.myapp.MainActivity  emptyMap com.example.myapp.MainActivity  executor com.example.myapp.MainActivity  get com.example.myapp.MainActivity  getCurrentLocation com.example.myapp.MainActivity  getModelFilePath com.example.myapp.MainActivity  getSystemService com.example.myapp.MainActivity  initLlmModel com.example.myapp.MainActivity  inputStream com.example.myapp.MainActivity  isLocationServiceEnabled com.example.myapp.MainActivity  
isNotEmpty com.example.myapp.MainActivity  
isNullOrBlank com.example.myapp.MainActivity  java com.example.myapp.MainActivity  	javaClass com.example.myapp.MainActivity  listOf com.example.myapp.MainActivity  llmInference com.example.myapp.MainActivity  llmLock com.example.myapp.MainActivity  
llmSession com.example.myapp.MainActivity  locationManager com.example.myapp.MainActivity  mapOf com.example.myapp.MainActivity  modelLoaded com.example.myapp.MainActivity  outputStream com.example.myapp.MainActivity  printLog com.example.myapp.MainActivity  	readBytes com.example.myapp.MainActivity  runLlmInference com.example.myapp.MainActivity  synchronized com.example.myapp.MainActivity  to com.example.myapp.MainActivity  transcribeWavWithVosk com.example.myapp.MainActivity  use com.example.myapp.MainActivity  Result com.example.myapp.MethodChannel  content com.example.myapp.android  res !com.example.myapp.android.content  AssetManager %com.example.myapp.android.content.res  BitmapImageBuilder $com.google.mediapipe.framework.image  build 7com.google.mediapipe.framework.image.BitmapImageBuilder  GraphOptions -com.google.mediapipe.tasks.genai.llminference  LlmInference -com.google.mediapipe.tasks.genai.llminference  LlmInferenceSession -com.google.mediapipe.tasks.genai.llminference  ProgressListener -com.google.mediapipe.tasks.genai.llminference  builder :com.google.mediapipe.tasks.genai.llminference.GraphOptions  build Bcom.google.mediapipe.tasks.genai.llminference.GraphOptions.Builder  setEnableVisionModality Bcom.google.mediapipe.tasks.genai.llminference.GraphOptions.Builder  Backend :com.google.mediapipe.tasks.genai.llminference.LlmInference  createFromOptions :com.google.mediapipe.tasks.genai.llminference.LlmInference  CPU Bcom.google.mediapipe.tasks.genai.llminference.LlmInference.Backend  builder Ncom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions  build Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setMaxNumImages Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setMaxTokens Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  
setMaxTopK Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setModelPath Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setPreferredBackend Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  addImage Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  
addQueryChunk Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  createFromOptions Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  generateResponseAsync Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  	javaClass Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  builder \com.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions  build dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setGraphOptions dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setTemperature dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setTopK dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setTopP dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  <SAM-CONSTRUCTOR> >com.google.mediapipe.tasks.genai.llminference.ProgressListener  FlutterActivity io.flutter.embedding.android  ActivityCompat ,io.flutter.embedding.android.FlutterActivity  Any ,io.flutter.embedding.android.FlutterActivity  
BitmapFactory ,io.flutter.embedding.android.FlutterActivity  Bundle ,io.flutter.embedding.android.FlutterActivity  	ByteArray ,io.flutter.embedding.android.FlutterActivity  Context ,io.flutter.embedding.android.FlutterActivity  	Exception ,io.flutter.embedding.android.FlutterActivity  	Executors ,io.flutter.embedding.android.FlutterActivity  GeneratedPluginRegistrant ,io.flutter.embedding.android.FlutterActivity  GraphOptions ,io.flutter.embedding.android.FlutterActivity  Handler ,io.flutter.embedding.android.FlutterActivity  Int ,io.flutter.embedding.android.FlutterActivity  LlmInference ,io.flutter.embedding.android.FlutterActivity  LlmInferenceSession ,io.flutter.embedding.android.FlutterActivity  Location ,io.flutter.embedding.android.FlutterActivity  LocationListener ,io.flutter.embedding.android.FlutterActivity  LocationManager ,io.flutter.embedding.android.FlutterActivity  Log ,io.flutter.embedding.android.FlutterActivity  Looper ,io.flutter.embedding.android.FlutterActivity  Manifest ,io.flutter.embedding.android.FlutterActivity  Map ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  Model ,io.flutter.embedding.android.FlutterActivity  NoSuchMethodException ,io.flutter.embedding.android.FlutterActivity  PackageManager ,io.flutter.embedding.android.FlutterActivity  
Recognizer ,io.flutter.embedding.android.FlutterActivity  Regex ,io.flutter.embedding.android.FlutterActivity  String ,io.flutter.embedding.android.FlutterActivity  
StringBuilder ,io.flutter.embedding.android.FlutterActivity  System ,io.flutter.embedding.android.FlutterActivity  also ,io.flutter.embedding.android.FlutterActivity  android ,io.flutter.embedding.android.FlutterActivity  com ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  copyTo ,io.flutter.embedding.android.FlutterActivity  emptyMap ,io.flutter.embedding.android.FlutterActivity  get ,io.flutter.embedding.android.FlutterActivity  inputStream ,io.flutter.embedding.android.FlutterActivity  
isNotEmpty ,io.flutter.embedding.android.FlutterActivity  
isNullOrBlank ,io.flutter.embedding.android.FlutterActivity  java ,io.flutter.embedding.android.FlutterActivity  	javaClass ,io.flutter.embedding.android.FlutterActivity  listOf ,io.flutter.embedding.android.FlutterActivity  locationManager ,io.flutter.embedding.android.FlutterActivity  mapOf ,io.flutter.embedding.android.FlutterActivity  outputStream ,io.flutter.embedding.android.FlutterActivity  	readBytes ,io.flutter.embedding.android.FlutterActivity  synchronized ,io.flutter.embedding.android.FlutterActivity  to ,io.flutter.embedding.android.FlutterActivity  use ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  DartExecutor  io.flutter.embedding.engine.dart  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  GeneratedPluginRegistrant io.flutter.plugins  registerWith ,io.flutter.plugins.GeneratedPluginRegistrant  ByteArrayInputStream java.io  File java.io  FileInputStream java.io  FileOutputStream java.io  InputStream java.io  absolutePath java.io.File  copyTo java.io.File  exists java.io.File  inputStream java.io.File  length java.io.File  mkdirs java.io.File  outputStream java.io.File  	readBytes java.io.File  close java.io.FileInputStream  read java.io.FileInputStream  use java.io.FileOutputStream  copyTo java.io.InputStream  use java.io.InputStream  Class 	java.lang  	Exception 	java.lang  NoSuchMethodException 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  	getMethod java.lang.Class  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  length java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  invoke java.lang.reflect.Method  CountDownLatch java.util.concurrent  	Executors java.util.concurrent  await #java.util.concurrent.CountDownLatch  	countDown #java.util.concurrent.CountDownLatch  execute java.util.concurrent.Executor  newSingleThreadExecutor java.util.concurrent.Executors  	ByteArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  also kotlin  synchronized kotlin  to kotlin  use kotlin  iterator kotlin.Array  not kotlin.Boolean  	compareTo kotlin.Float  also 
kotlin.Int  	compareTo 
kotlin.Int  minus kotlin.Long  
isNullOrBlank 
kotlin.String  to 
kotlin.String  message kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  emptyMap kotlin.collections  get kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  get kotlin.collections.List  iterator kotlin.collections.List  get kotlin.collections.Map  copyTo 	kotlin.io  inputStream 	kotlin.io  outputStream 	kotlin.io  	readBytes 	kotlin.io  use 	kotlin.io  java 
kotlin.jvm  	javaClass 
kotlin.jvm  KClass kotlin.reflect  java kotlin.reflect.KClass  
MatchGroup kotlin.text  MatchResult kotlin.text  Regex kotlin.text  get kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  Model org.vosk  
Recognizer org.vosk  close org.vosk.Model  acceptWaveForm org.vosk.Recognizer  close org.vosk.Recognizer  finalResult org.vosk.Recognizer  
plusAssign android.app.Activity  
plusAssign android.content.Context  
plusAssign android.content.ContextWrapper  
plusAssign  android.view.ContextThemeWrapper  
plusAssign com.example.myapp  
plusAssign com.example.myapp.MainActivity  
plusAssign ,io.flutter.embedding.android.FlutterActivity  printStackTrace java.lang.Exception  plus 
kotlin.Int  
plusAssign 
kotlin.Int  length 
kotlin.String  printStackTrace kotlin.Throwable  
plusAssign kotlin.collections  
runOnUiThread android.app.Activity  
runOnUiThread com.example.myapp.MainActivity  int java.nio.ByteBuffer  order java.nio.ByteBuffer  short java.nio.ByteBuffer  wrap java.nio.ByteBuffer  
LITTLE_ENDIAN java.nio.ByteOrder  toInt kotlin.Short  RegexOption android.app.Activity  indexOf android.app.Activity  isEmpty android.app.Activity  	substring android.app.Activity  RegexOption android.content.Context  indexOf android.content.Context  isEmpty android.content.Context  	substring android.content.Context  RegexOption android.content.ContextWrapper  indexOf android.content.ContextWrapper  isEmpty android.content.ContextWrapper  	substring android.content.ContextWrapper  RegexOption  android.view.ContextThemeWrapper  indexOf  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  	substring  android.view.ContextThemeWrapper  RegexOption com.example.myapp  indexOf com.example.myapp  isEmpty com.example.myapp  	substring com.example.myapp  RegexOption com.example.myapp.MainActivity  indexOf com.example.myapp.MainActivity  isEmpty com.example.myapp.MainActivity  	substring com.example.myapp.MainActivity  RegexOption ,io.flutter.embedding.android.FlutterActivity  indexOf ,io.flutter.embedding.android.FlutterActivity  isEmpty ,io.flutter.embedding.android.FlutterActivity  	substring ,io.flutter.embedding.android.FlutterActivity  isEmpty kotlin.CharSequence  isEmpty 
kotlin.String  indexOf kotlin.collections  isEmpty kotlin.collections  indexOf kotlin.sequences  RegexOption kotlin.text  indexOf kotlin.text  isEmpty kotlin.text  	substring kotlin.text  DOT_MATCHES_ALL kotlin.text.RegexOption                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          