  Context android.content  assets android.content.Context  AssetManager android.content.res  open  android.content.res.AssetManager  Handler 
android.os  Looper 
android.os  post android.os.Handler  
getMainLooper android.os.Looper  Any com.example.large_file_handler  AssetManager com.example.large_file_handler  	ByteArray com.example.large_file_handler  CoroutineScope com.example.large_file_handler  Dispatchers com.example.large_file_handler  EventChannel com.example.large_file_handler  	Exception com.example.large_file_handler  File com.example.large_file_handler  FileOutputStream com.example.large_file_handler  
FlutterLoader com.example.large_file_handler  
FlutterPlugin com.example.large_file_handler  Handler com.example.large_file_handler  IOException com.example.large_file_handler  InputStream com.example.large_file_handler  Int com.example.large_file_handler  LargeFileHandlerPlugin com.example.large_file_handler  Long com.example.large_file_handler  Looper com.example.large_file_handler  
MethodCall com.example.large_file_handler  MethodCallHandler com.example.large_file_handler  
MethodChannel com.example.large_file_handler  OkHttpClient com.example.large_file_handler  Request com.example.large_file_handler  Result com.example.large_file_handler  String com.example.large_file_handler  also com.example.large_file_handler  assetManager com.example.large_file_handler  copyStreamToFile com.example.large_file_handler  copyStreamToFileWithProgress com.example.large_file_handler  downloadFileFromUrl com.example.large_file_handler  
flutterLoader com.example.large_file_handler  getContentLength com.example.large_file_handler  handler com.example.large_file_handler  launch com.example.large_file_handler  
plusAssign com.example.large_file_handler  toLong com.example.large_file_handler  use com.example.large_file_handler  	EventSink +com.example.large_file_handler.EventChannel  
StreamHandler +com.example.large_file_handler.EventChannel  FlutterPluginBinding ,com.example.large_file_handler.FlutterPlugin  	ByteArray 5com.example.large_file_handler.LargeFileHandlerPlugin  CoroutineScope 5com.example.large_file_handler.LargeFileHandlerPlugin  Dispatchers 5com.example.large_file_handler.LargeFileHandlerPlugin  EventChannel 5com.example.large_file_handler.LargeFileHandlerPlugin  File 5com.example.large_file_handler.LargeFileHandlerPlugin  FileOutputStream 5com.example.large_file_handler.LargeFileHandlerPlugin  
FlutterLoader 5com.example.large_file_handler.LargeFileHandlerPlugin  Handler 5com.example.large_file_handler.LargeFileHandlerPlugin  IOException 5com.example.large_file_handler.LargeFileHandlerPlugin  Looper 5com.example.large_file_handler.LargeFileHandlerPlugin  
MethodChannel 5com.example.large_file_handler.LargeFileHandlerPlugin  OkHttpClient 5com.example.large_file_handler.LargeFileHandlerPlugin  Request 5com.example.large_file_handler.LargeFileHandlerPlugin  also 5com.example.large_file_handler.LargeFileHandlerPlugin  assetManager 5com.example.large_file_handler.LargeFileHandlerPlugin  channel 5com.example.large_file_handler.LargeFileHandlerPlugin  copyStreamToFile 5com.example.large_file_handler.LargeFileHandlerPlugin  copyStreamToFileWithProgress 5com.example.large_file_handler.LargeFileHandlerPlugin  downloadFileFromUrl 5com.example.large_file_handler.LargeFileHandlerPlugin  	eventSink 5com.example.large_file_handler.LargeFileHandlerPlugin  
flutterLoader 5com.example.large_file_handler.LargeFileHandlerPlugin  getContentLength 5com.example.large_file_handler.LargeFileHandlerPlugin  handler 5com.example.large_file_handler.LargeFileHandlerPlugin  launch 5com.example.large_file_handler.LargeFileHandlerPlugin  
plusAssign 5com.example.large_file_handler.LargeFileHandlerPlugin  toLong 5com.example.large_file_handler.LargeFileHandlerPlugin  use 5com.example.large_file_handler.LargeFileHandlerPlugin  
FlutterLoader "io.flutter.embedding.engine.loader  ensureInitializationComplete 0io.flutter.embedding.engine.loader.FlutterLoader  getLookupKeyForAsset 0io.flutter.embedding.engine.loader.FlutterLoader  startInitialization 0io.flutter.embedding.engine.loader.FlutterLoader  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  endOfStream /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  exists java.io.File  use java.io.FileOutputStream  write java.io.FileOutputStream  	available java.io.InputStream  read java.io.InputStream  use java.io.InputStream  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  	ByteArray kotlin  	Function0 kotlin  	Function1 kotlin  also kotlin  use kotlin  not kotlin.Boolean  also 
kotlin.Int  	compareTo 
kotlin.Int  toLong 
kotlin.Int  div kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  times kotlin.Long  toInt kotlin.Long  toLong 
kotlin.String  message kotlin.Throwable  
plusAssign kotlin.collections  SuspendFunction1 kotlin.coroutines  use 	kotlin.io  toLong kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  assetManager !kotlinx.coroutines.CoroutineScope  copyStreamToFile !kotlinx.coroutines.CoroutineScope  copyStreamToFileWithProgress !kotlinx.coroutines.CoroutineScope  downloadFileFromUrl !kotlinx.coroutines.CoroutineScope  
flutterLoader !kotlinx.coroutines.CoroutineScope  getContentLength !kotlinx.coroutines.CoroutineScope  handler !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Call okhttp3  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  ResponseBody okhttp3  execute okhttp3.Call  newCall okhttp3.OkHttpClient  Builder okhttp3.Request  build okhttp3.Request.Builder  head okhttp3.Request.Builder  url okhttp3.Request.Builder  body okhttp3.Response  code okhttp3.Response  header okhttp3.Response  isSuccessful okhttp3.Response  
byteStream okhttp3.ResponseBody                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         