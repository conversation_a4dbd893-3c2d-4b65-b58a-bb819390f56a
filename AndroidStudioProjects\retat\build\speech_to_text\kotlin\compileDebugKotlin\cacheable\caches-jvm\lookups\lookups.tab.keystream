  Manifest android  BLUETOOTH_CONNECT android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  data android.R.attr  	TargetApi android.annotation  Activity android.app  	RESULT_OK android.app.Activity  BluetoothAdapter android.bluetooth  BluetoothDevice android.bluetooth  BluetoothHeadset android.bluetooth  BluetoothProfile android.bluetooth  getBondedDevices "android.bluetooth.BluetoothAdapter  getDefaultAdapter "android.bluetooth.BluetoothAdapter  getProfileProxy "android.bluetooth.BluetoothAdapter  	isEnabled "android.bluetooth.BluetoothAdapter  startVoiceRecognition "android.bluetooth.BluetoothHeadset  stopVoiceRecognition "android.bluetooth.BluetoothHeadset  toString "android.bluetooth.BluetoothHeadset  HEADSET "android.bluetooth.BluetoothProfile  ServiceListener "android.bluetooth.BluetoothProfile  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  	ArrayList !android.content.BroadcastReceiver  Locale !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  RecognizerIntent !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  getResultExtras !android.content.BroadcastReceiver  replace !android.content.BroadcastReceiver  
ComponentName android.content.Context  Intent android.content.Context  RecognitionService android.content.Context  applicationInfo android.content.Context  debugLog android.content.Context  findComponentName android.content.Context  firstOrNull android.content.Context  let android.content.Context  packageManager android.content.Context  sendOrderedBroadcast android.content.Context  
ListenMode android.content.Intent  Locale android.content.Intent  RecognizerIntent android.content.Intent  apply android.content.Intent  debugLog android.content.Intent  
pluginContext android.content.Intent  putExtra android.content.Intent  
setPackage android.content.Intent  PackageManager android.content.pm  ResolveInfo android.content.pm  ServiceInfo android.content.pm  name "android.content.pm.PackageItemInfo  packageName "android.content.pm.PackageItemInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  queryIntentServices !android.content.pm.PackageManager  serviceInfo android.content.pm.ResolveInfo  let android.content.pm.ServiceInfo  name android.content.pm.ServiceInfo  packageName android.content.pm.ServiceInfo  Uri android.net  Build 
android.os  Bundle 
android.os  Handler 
android.os  Looper 
android.os  containsKey android.os.BaseBundle  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
getFloatArray android.os.Bundle  getStringArrayList android.os.Bundle  post android.os.Handler  postDelayed android.os.Handler  
getMainLooper android.os.Looper  Activity android.speech  
ActivityAware android.speech  ActivityCompat android.speech  ActivityPluginBinding android.speech  Any android.speech  Array android.speech  	ArrayList android.speech  BinaryMessenger android.speech  BluetoothAdapter android.speech  BluetoothHeadset android.speech  BluetoothProfile android.speech  Boolean android.speech  BroadcastReceiver android.speech  Build android.speech  Bundle android.speech  	ByteArray android.speech  ChannelResultWrapper android.speech  
ComponentName android.speech  Context android.speech  
ContextCompat android.speech  Double android.speech  	Exception android.speech  	Executors android.speech  Float android.speech  
FlutterPlugin android.speech  Handler android.speech  Int android.speech  IntArray android.speech  Intent android.speech  	JSONArray android.speech  
JSONObject android.speech  LanguageDetailsChecker android.speech  List android.speech  
ListenMode android.speech  Locale android.speech  Log android.speech  Long android.speech  Looper android.speech  Manifest android.speech  
MethodCall android.speech  MethodCallHandler android.speech  
MethodChannel android.speech  NonNull android.speech  PackageManager android.speech  PluginRegistry android.speech  RecognitionListener android.speech  RecognitionService android.speech  RecognitionSupport android.speech  RecognitionSupportCallback android.speech  RecognizerIntent android.speech  ResolveInfo android.speech  Result android.speech  Set android.speech  SpeechRecognizer android.speech  SpeechToTextCallbackMethods android.speech  SpeechToTextErrors android.speech  SpeechToTextStatus android.speech  String android.speech  System android.speech  	TargetApi android.speech  android android.speech  apply android.speech  arrayOf android.speech  bluetoothHeadset android.speech  createOnDeviceSpeechRecognizer android.speech  createSpeechRecognizer android.speech  debugLog android.speech  debugLogging android.speech  
enumValues android.speech  firstOrNull android.speech  forEach android.speech  isEmpty android.speech  
isNotEmpty android.speech  let android.speech  pluginChannelName android.speech  
pluginContext android.speech  plus android.speech  replace android.speech  run android.speech  toString android.speech  ServiceListener android.speech.BluetoothProfile  FlutterPluginBinding android.speech.FlutterPlugin   RequestPermissionsResultListener android.speech.PluginRegistry  SERVICE_INTERFACE !android.speech.RecognitionService  supportedOnDeviceLanguages !android.speech.RecognitionSupport  ACTION_GET_LANGUAGE_DETAILS android.speech.RecognizerIntent  ACTION_RECOGNIZE_SPEECH android.speech.RecognizerIntent  EXTRA_CALLING_PACKAGE android.speech.RecognizerIntent  EXTRA_LANGUAGE android.speech.RecognizerIntent  EXTRA_LANGUAGE_MODEL android.speech.RecognizerIntent  EXTRA_LANGUAGE_PREFERENCE android.speech.RecognizerIntent  EXTRA_MAX_RESULTS android.speech.RecognizerIntent  EXTRA_PARTIAL_RESULTS android.speech.RecognizerIntent  EXTRA_PREFER_OFFLINE android.speech.RecognizerIntent  EXTRA_SUPPORTED_LANGUAGES android.speech.RecognizerIntent  LANGUAGE_MODEL_FREE_FORM android.speech.RecognizerIntent  LANGUAGE_MODEL_WEB_SEARCH android.speech.RecognizerIntent  getVoiceDetailsIntent android.speech.RecognizerIntent  CONFIDENCE_SCORES android.speech.SpeechRecognizer  ERROR_AUDIO android.speech.SpeechRecognizer  ERROR_CLIENT android.speech.SpeechRecognizer  ERROR_INSUFFICIENT_PERMISSIONS android.speech.SpeechRecognizer  ERROR_LANGUAGE_NOT_SUPPORTED android.speech.SpeechRecognizer  ERROR_LANGUAGE_UNAVAILABLE android.speech.SpeechRecognizer  
ERROR_NETWORK android.speech.SpeechRecognizer  ERROR_NETWORK_TIMEOUT android.speech.SpeechRecognizer  ERROR_NO_MATCH android.speech.SpeechRecognizer  ERROR_RECOGNIZER_BUSY android.speech.SpeechRecognizer  ERROR_SERVER android.speech.SpeechRecognizer  ERROR_SERVER_DISCONNECTED android.speech.SpeechRecognizer  ERROR_SPEECH_TIMEOUT android.speech.SpeechRecognizer  ERROR_TOO_MANY_REQUESTS android.speech.SpeechRecognizer  RESULTS_RECOGNITION android.speech.SpeechRecognizer  apply android.speech.SpeechRecognizer  cancel android.speech.SpeechRecognizer  checkRecognitionSupport android.speech.SpeechRecognizer  createOnDeviceSpeechRecognizer android.speech.SpeechRecognizer  createSpeechRecognizer android.speech.SpeechRecognizer  debugLog android.speech.SpeechRecognizer  destroy android.speech.SpeechRecognizer  isOnDeviceRecognitionAvailable android.speech.SpeechRecognizer  isRecognitionAvailable android.speech.SpeechRecognizer  setRecognitionListener android.speech.SpeechRecognizer  startListening android.speech.SpeechRecognizer  
stopListening android.speech.SpeechRecognizer  	bluetooth android.speech.android  BluetoothAdapter  android.speech.android.bluetooth  BluetoothDevice  android.speech.android.bluetooth  Log android.util  d android.util.Log  e android.util.Log  NonNull androidx.annotation  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  Activity com.csdcorp.speech_to_text  
ActivityAware com.csdcorp.speech_to_text  ActivityCompat com.csdcorp.speech_to_text  ActivityPluginBinding com.csdcorp.speech_to_text  Any com.csdcorp.speech_to_text  Array com.csdcorp.speech_to_text  	ArrayList com.csdcorp.speech_to_text  BinaryMessenger com.csdcorp.speech_to_text  BluetoothAdapter com.csdcorp.speech_to_text  BluetoothHeadset com.csdcorp.speech_to_text  BluetoothProfile com.csdcorp.speech_to_text  Boolean com.csdcorp.speech_to_text  BroadcastReceiver com.csdcorp.speech_to_text  Build com.csdcorp.speech_to_text  Bundle com.csdcorp.speech_to_text  	ByteArray com.csdcorp.speech_to_text  ChannelResultWrapper com.csdcorp.speech_to_text  
ComponentName com.csdcorp.speech_to_text  Context com.csdcorp.speech_to_text  
ContextCompat com.csdcorp.speech_to_text  Double com.csdcorp.speech_to_text  	Exception com.csdcorp.speech_to_text  	Executors com.csdcorp.speech_to_text  Float com.csdcorp.speech_to_text  
FlutterPlugin com.csdcorp.speech_to_text  Handler com.csdcorp.speech_to_text  Int com.csdcorp.speech_to_text  IntArray com.csdcorp.speech_to_text  Intent com.csdcorp.speech_to_text  	JSONArray com.csdcorp.speech_to_text  
JSONObject com.csdcorp.speech_to_text  LanguageDetailsChecker com.csdcorp.speech_to_text  List com.csdcorp.speech_to_text  
ListenMode com.csdcorp.speech_to_text  Locale com.csdcorp.speech_to_text  Log com.csdcorp.speech_to_text  Long com.csdcorp.speech_to_text  Looper com.csdcorp.speech_to_text  Manifest com.csdcorp.speech_to_text  
MethodCall com.csdcorp.speech_to_text  MethodCallHandler com.csdcorp.speech_to_text  
MethodChannel com.csdcorp.speech_to_text  NonNull com.csdcorp.speech_to_text  PackageManager com.csdcorp.speech_to_text  PluginRegistry com.csdcorp.speech_to_text  RecognitionListener com.csdcorp.speech_to_text  RecognitionService com.csdcorp.speech_to_text  RecognitionSupport com.csdcorp.speech_to_text  RecognitionSupportCallback com.csdcorp.speech_to_text  RecognizerIntent com.csdcorp.speech_to_text  ResolveInfo com.csdcorp.speech_to_text  Result com.csdcorp.speech_to_text  Set com.csdcorp.speech_to_text  SpeechRecognizer com.csdcorp.speech_to_text  SpeechToTextCallbackMethods com.csdcorp.speech_to_text  SpeechToTextErrors com.csdcorp.speech_to_text  SpeechToTextPlugin com.csdcorp.speech_to_text  SpeechToTextStatus com.csdcorp.speech_to_text  String com.csdcorp.speech_to_text  System com.csdcorp.speech_to_text  	TargetApi com.csdcorp.speech_to_text  android com.csdcorp.speech_to_text  apply com.csdcorp.speech_to_text  arrayOf com.csdcorp.speech_to_text  bluetoothHeadset com.csdcorp.speech_to_text  createOnDeviceSpeechRecognizer com.csdcorp.speech_to_text  createSpeechRecognizer com.csdcorp.speech_to_text  debugLog com.csdcorp.speech_to_text  debugLogging com.csdcorp.speech_to_text  
enumValues com.csdcorp.speech_to_text  firstOrNull com.csdcorp.speech_to_text  forEach com.csdcorp.speech_to_text  isEmpty com.csdcorp.speech_to_text  
isNotEmpty com.csdcorp.speech_to_text  let com.csdcorp.speech_to_text  pluginChannelName com.csdcorp.speech_to_text  
pluginContext com.csdcorp.speech_to_text  plus com.csdcorp.speech_to_text  replace com.csdcorp.speech_to_text  run com.csdcorp.speech_to_text  toString com.csdcorp.speech_to_text  ServiceListener +com.csdcorp.speech_to_text.BluetoothProfile  Handler /com.csdcorp.speech_to_text.ChannelResultWrapper  Looper /com.csdcorp.speech_to_text.ChannelResultWrapper  error /com.csdcorp.speech_to_text.ChannelResultWrapper  handler /com.csdcorp.speech_to_text.ChannelResultWrapper  notImplemented /com.csdcorp.speech_to_text.ChannelResultWrapper  result /com.csdcorp.speech_to_text.ChannelResultWrapper  run /com.csdcorp.speech_to_text.ChannelResultWrapper  FlutterPluginBinding (com.csdcorp.speech_to_text.FlutterPlugin  	ArrayList 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Locale 1com.csdcorp.speech_to_text.LanguageDetailsChecker  Log 1com.csdcorp.speech_to_text.LanguageDetailsChecker  RecognizerIntent 1com.csdcorp.speech_to_text.LanguageDetailsChecker  buildIdNameForLocale 1com.csdcorp.speech_to_text.LanguageDetailsChecker  createResponse 1com.csdcorp.speech_to_text.LanguageDetailsChecker  debugLog 1com.csdcorp.speech_to_text.LanguageDetailsChecker  debugLogging 1com.csdcorp.speech_to_text.LanguageDetailsChecker  getResultExtras 1com.csdcorp.speech_to_text.LanguageDetailsChecker  languagePreference 1com.csdcorp.speech_to_text.LanguageDetailsChecker  logTag 1com.csdcorp.speech_to_text.LanguageDetailsChecker  replace 1com.csdcorp.speech_to_text.LanguageDetailsChecker  result 1com.csdcorp.speech_to_text.LanguageDetailsChecker  supportedLanguages 1com.csdcorp.speech_to_text.LanguageDetailsChecker  
deviceDefault %com.csdcorp.speech_to_text.ListenMode  search %com.csdcorp.speech_to_text.ListenMode   RequestPermissionsResultListener )com.csdcorp.speech_to_text.PluginRegistry  name 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  notifyError 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  notifyStatus 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  soundLevelChange 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  textRecognition 6com.csdcorp.speech_to_text.SpeechToTextCallbackMethods  missingContext -com.csdcorp.speech_to_text.SpeechToTextErrors  missingOrInvalidArg -com.csdcorp.speech_to_text.SpeechToTextErrors  multipleRequests -com.csdcorp.speech_to_text.SpeechToTextErrors  name -com.csdcorp.speech_to_text.SpeechToTextErrors  recognizerNotAvailable -com.csdcorp.speech_to_text.SpeechToTextErrors  unknown -com.csdcorp.speech_to_text.SpeechToTextErrors  Activity -com.csdcorp.speech_to_text.SpeechToTextPlugin  ActivityCompat -com.csdcorp.speech_to_text.SpeechToTextPlugin  BluetoothAdapter -com.csdcorp.speech_to_text.SpeechToTextPlugin  BluetoothProfile -com.csdcorp.speech_to_text.SpeechToTextPlugin  Build -com.csdcorp.speech_to_text.SpeechToTextPlugin  ChannelResultWrapper -com.csdcorp.speech_to_text.SpeechToTextPlugin  
ComponentName -com.csdcorp.speech_to_text.SpeechToTextPlugin  
ContextCompat -com.csdcorp.speech_to_text.SpeechToTextPlugin  	Executors -com.csdcorp.speech_to_text.SpeechToTextPlugin  Handler -com.csdcorp.speech_to_text.SpeechToTextPlugin  Intent -com.csdcorp.speech_to_text.SpeechToTextPlugin  	JSONArray -com.csdcorp.speech_to_text.SpeechToTextPlugin  
JSONObject -com.csdcorp.speech_to_text.SpeechToTextPlugin  LanguageDetailsChecker -com.csdcorp.speech_to_text.SpeechToTextPlugin  
ListenMode -com.csdcorp.speech_to_text.SpeechToTextPlugin  Locale -com.csdcorp.speech_to_text.SpeechToTextPlugin  Log -com.csdcorp.speech_to_text.SpeechToTextPlugin  Looper -com.csdcorp.speech_to_text.SpeechToTextPlugin  Manifest -com.csdcorp.speech_to_text.SpeechToTextPlugin  
MethodChannel -com.csdcorp.speech_to_text.SpeechToTextPlugin  PackageManager -com.csdcorp.speech_to_text.SpeechToTextPlugin  RecognitionService -com.csdcorp.speech_to_text.SpeechToTextPlugin  RecognizerIntent -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechToTextCallbackMethods -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechToTextErrors -com.csdcorp.speech_to_text.SpeechToTextPlugin  SpeechToTextStatus -com.csdcorp.speech_to_text.SpeechToTextPlugin  System -com.csdcorp.speech_to_text.SpeechToTextPlugin  activeBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  activeResult -com.csdcorp.speech_to_text.SpeechToTextPlugin  
alwaysUseStop -com.csdcorp.speech_to_text.SpeechToTextPlugin  apply -com.csdcorp.speech_to_text.SpeechToTextPlugin  arrayOf -com.csdcorp.speech_to_text.SpeechToTextPlugin  bluetoothAdapter -com.csdcorp.speech_to_text.SpeechToTextPlugin  bluetoothDisabled -com.csdcorp.speech_to_text.SpeechToTextPlugin  bluetoothHeadset -com.csdcorp.speech_to_text.SpeechToTextPlugin  
brokenStopSdk -com.csdcorp.speech_to_text.SpeechToTextPlugin  cancelListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  channel -com.csdcorp.speech_to_text.SpeechToTextPlugin  completeInitialize -com.csdcorp.speech_to_text.SpeechToTextPlugin  createOnDeviceSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  createRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  createSpeechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  currentActivity -com.csdcorp.speech_to_text.SpeechToTextPlugin  debugLog -com.csdcorp.speech_to_text.SpeechToTextPlugin  debugLogging -com.csdcorp.speech_to_text.SpeechToTextPlugin  defaultLanguageTag -com.csdcorp.speech_to_text.SpeechToTextPlugin  destroyRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  
enumValues -com.csdcorp.speech_to_text.SpeechToTextPlugin  findComponentName -com.csdcorp.speech_to_text.SpeechToTextPlugin  firstOrNull -com.csdcorp.speech_to_text.SpeechToTextPlugin  handler -com.csdcorp.speech_to_text.SpeechToTextPlugin  
hasPermission -com.csdcorp.speech_to_text.SpeechToTextPlugin  
initialize -com.csdcorp.speech_to_text.SpeechToTextPlugin  initializeIfPermitted -com.csdcorp.speech_to_text.SpeechToTextPlugin  initializedSuccessfully -com.csdcorp.speech_to_text.SpeechToTextPlugin  intentLookup -com.csdcorp.speech_to_text.SpeechToTextPlugin  isDuplicateFinal -com.csdcorp.speech_to_text.SpeechToTextPlugin  isEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  isListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  
isNotEmpty -com.csdcorp.speech_to_text.SpeechToTextPlugin  isNotInitialized -com.csdcorp.speech_to_text.SpeechToTextPlugin  isNotListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  
lastFinalTime -com.csdcorp.speech_to_text.SpeechToTextPlugin  lastOnDevice -com.csdcorp.speech_to_text.SpeechToTextPlugin  let -com.csdcorp.speech_to_text.SpeechToTextPlugin  	listening -com.csdcorp.speech_to_text.SpeechToTextPlugin  locales -com.csdcorp.speech_to_text.SpeechToTextPlugin  logTag -com.csdcorp.speech_to_text.SpeechToTextPlugin  maxRms -com.csdcorp.speech_to_text.SpeechToTextPlugin  minRms -com.csdcorp.speech_to_text.SpeechToTextPlugin  minSdkForSpeechSupport -com.csdcorp.speech_to_text.SpeechToTextPlugin  missingConfidence -com.csdcorp.speech_to_text.SpeechToTextPlugin  noBluetoothOpt -com.csdcorp.speech_to_text.SpeechToTextPlugin  notifyListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  onAttachedToEngine -com.csdcorp.speech_to_text.SpeechToTextPlugin  optionallyStartBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  optionallyStopBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  
pairedDevices -com.csdcorp.speech_to_text.SpeechToTextPlugin  permissionToRecordAudio -com.csdcorp.speech_to_text.SpeechToTextPlugin  pluginChannelName -com.csdcorp.speech_to_text.SpeechToTextPlugin  
pluginContext -com.csdcorp.speech_to_text.SpeechToTextPlugin  plus -com.csdcorp.speech_to_text.SpeechToTextPlugin  previousListenMode -com.csdcorp.speech_to_text.SpeechToTextPlugin  previousPartialResults -com.csdcorp.speech_to_text.SpeechToTextPlugin  previousRecognizerLang -com.csdcorp.speech_to_text.SpeechToTextPlugin  recognizerIntent -com.csdcorp.speech_to_text.SpeechToTextPlugin  recognizerStops -com.csdcorp.speech_to_text.SpeechToTextPlugin  replace -com.csdcorp.speech_to_text.SpeechToTextPlugin  
resultSent -com.csdcorp.speech_to_text.SpeechToTextPlugin  run -com.csdcorp.speech_to_text.SpeechToTextPlugin  sdkVersionTooLow -com.csdcorp.speech_to_text.SpeechToTextPlugin  	sendError -com.csdcorp.speech_to_text.SpeechToTextPlugin  setupBluetooth -com.csdcorp.speech_to_text.SpeechToTextPlugin  setupRecognizerIntent -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechRecognizer -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechStartTime -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechThresholdRms -com.csdcorp.speech_to_text.SpeechToTextPlugin  speechToTextPermissionCode -com.csdcorp.speech_to_text.SpeechToTextPlugin  startListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  
stopListening -com.csdcorp.speech_to_text.SpeechToTextPlugin  toString -com.csdcorp.speech_to_text.SpeechToTextPlugin  
updateResults -com.csdcorp.speech_to_text.SpeechToTextPlugin  done -com.csdcorp.speech_to_text.SpeechToTextStatus  doneNoResult -com.csdcorp.speech_to_text.SpeechToTextStatus  	listening -com.csdcorp.speech_to_text.SpeechToTextStatus  name -com.csdcorp.speech_to_text.SpeechToTextStatus  notListening -com.csdcorp.speech_to_text.SpeechToTextStatus  	bluetooth "com.csdcorp.speech_to_text.android  BluetoothAdapter ,com.csdcorp.speech_to_text.android.bluetooth  BluetoothDevice ,com.csdcorp.speech_to_text.android.bluetooth  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  	Exception 	java.lang  Runnable 	java.lang  localizedMessage java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Activity 	java.util  
ActivityAware 	java.util  ActivityCompat 	java.util  ActivityPluginBinding 	java.util  Any 	java.util  Array 	java.util  	ArrayList 	java.util  BinaryMessenger 	java.util  BluetoothAdapter 	java.util  BluetoothHeadset 	java.util  BluetoothProfile 	java.util  Boolean 	java.util  BroadcastReceiver 	java.util  Build 	java.util  Bundle 	java.util  	ByteArray 	java.util  ChannelResultWrapper 	java.util  
ComponentName 	java.util  Context 	java.util  
ContextCompat 	java.util  Double 	java.util  	Exception 	java.util  	Executors 	java.util  Float 	java.util  
FlutterPlugin 	java.util  Handler 	java.util  Int 	java.util  IntArray 	java.util  Intent 	java.util  	JSONArray 	java.util  
JSONObject 	java.util  LanguageDetailsChecker 	java.util  List 	java.util  
ListenMode 	java.util  Locale 	java.util  Log 	java.util  Long 	java.util  Looper 	java.util  Manifest 	java.util  
MethodCall 	java.util  MethodCallHandler 	java.util  
MethodChannel 	java.util  NonNull 	java.util  PackageManager 	java.util  PluginRegistry 	java.util  RecognitionListener 	java.util  RecognitionService 	java.util  RecognitionSupport 	java.util  RecognitionSupportCallback 	java.util  RecognizerIntent 	java.util  ResolveInfo 	java.util  Result 	java.util  Set 	java.util  SpeechRecognizer 	java.util  SpeechToTextCallbackMethods 	java.util  SpeechToTextErrors 	java.util  SpeechToTextStatus 	java.util  String 	java.util  System 	java.util  	TargetApi 	java.util  android 	java.util  apply 	java.util  arrayOf 	java.util  bluetoothHeadset 	java.util  createOnDeviceSpeechRecognizer 	java.util  createSpeechRecognizer 	java.util  debugLog 	java.util  debugLogging 	java.util  
enumValues 	java.util  firstOrNull 	java.util  forEach 	java.util  isEmpty 	java.util  
isNotEmpty 	java.util  let 	java.util  pluginChannelName 	java.util  
pluginContext 	java.util  plus 	java.util  replace 	java.util  run 	java.util  toString 	java.util  add java.util.ArrayList  get java.util.ArrayList  
isNotEmpty java.util.ArrayList  size java.util.ArrayList  ServiceListener java.util.BluetoothProfile  FlutterPluginBinding java.util.FlutterPlugin  country java.util.Locale  displayName java.util.Locale  forLanguageTag java.util.Locale  
getDefault java.util.Locale  language java.util.Locale  
toLanguageTag java.util.Locale   RequestPermissionsResultListener java.util.PluginRegistry  	bluetooth java.util.android  BluetoothAdapter java.util.android.bluetooth  BluetoothDevice java.util.android.bluetooth  	Executors java.util.concurrent  newSingleThreadExecutor java.util.concurrent.Executors  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  apply kotlin  arrayOf kotlin  
enumValues kotlin  let kotlin  plus kotlin  run kotlin  toString kotlin  toString 
kotlin.Any  get kotlin.Array  plus kotlin.Array  not kotlin.Boolean  
unaryMinus 
kotlin.Double  	compareTo kotlin.Float  
unaryMinus kotlin.Float  get kotlin.FloatArray  size kotlin.FloatArray  	compareTo 
kotlin.Int  minus 
kotlin.Int  rangeTo 
kotlin.Int  get kotlin.IntArray  isEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  size kotlin.IntArray  	compareTo kotlin.Long  minus kotlin.Long  plus 
kotlin.String  replace 
kotlin.String  localizedMessage kotlin.Throwable  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  plus kotlin.collections  toString kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  firstOrNull kotlin.collections.List  iterator kotlin.collections.List  size kotlin.collections.List  iterator kotlin.collections.Set  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  firstOrNull 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  Sequence kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  plus kotlin.sequences  firstOrNull kotlin.text  forEach kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  plus kotlin.text  replace kotlin.text  toString kotlin.text  	JSONArray org.json  
JSONObject org.json  put org.json.JSONArray  put org.json.JSONObject  toString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           