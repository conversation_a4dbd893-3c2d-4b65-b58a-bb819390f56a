package com.example.ai_comp_wearableos.presentation

import android.Manifest
import android.content.pm.PackageManager
import android.media.MediaRecorder
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.Stop
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.wear.compose.material.*
import androidx.wear.tooling.preview.devices.WearDevices
import androidx.wear.tooling.preview.Preview
import com.example.ai_comp_wearableos.presentation.theme.Ai_comp_wearableosTheme
import java.io.File
import java.io.IOException

class MainActivity : ComponentActivity() {
    private var mediaRecorder: MediaRecorder? = null
    private var audioFile: File? = null

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            Log.d("WearApp", "Audio permission granted")
        } else {
            Log.d("WearApp", "Audio permission denied")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen()
        super.onCreate(savedInstanceState)

        // Request audio permission
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            requestPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
        }

        setTheme(android.R.style.Theme_DeviceDefault)

        setContent {
            EmergencyWearApp(
                onStartRecording = { startRecording() },
                onStopRecording = { stopRecording() }
            )
        }
    }

    private fun startRecording() {
        try {
            // Create audio file
            audioFile = File(externalCacheDir, "emergency_voice_${System.currentTimeMillis()}.3gp")

            mediaRecorder = MediaRecorder().apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
                setOutputFile(audioFile?.absolutePath)
                setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)

                prepare()
                start()
            }
            Log.d("WearApp", "Recording started: ${audioFile?.absolutePath}")
        } catch (e: IOException) {
            Log.e("WearApp", "Recording failed: ${e.message}")
        }
    }

    private fun stopRecording() {
        try {
            mediaRecorder?.apply {
                stop()
                release()
            }
            mediaRecorder = null
            Log.d("WearApp", "Recording stopped: ${audioFile?.absolutePath}")

            // TODO: Send audio to phone app for processing
            processAudioWithPhone()

        } catch (e: Exception) {
            Log.e("WearApp", "Stop recording failed: ${e.message}")
        }
    }

    private fun processAudioWithPhone() {
        // TODO: Implement communication with phone app
        Log.d("WearApp", "Processing audio with phone app...")
        // This will be implemented with Data Layer API
    }
}

@Composable
fun EmergencyWearApp(
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit
) {
    var isRecording by remember { mutableStateOf(false) }
    var isProcessing by remember { mutableStateOf(false) }
    var smsResult by remember { mutableStateOf<String?>(null) }

    Ai_comp_wearableosTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colors.background),
            contentAlignment = Alignment.Center
        ) {
            when {
                smsResult != null -> {
                    // Show SMS result
                    SmsResultScreen(
                        sms = smsResult!!,
                        onBack = {
                            smsResult = null
                            isProcessing = false
                        }
                    )
                }
                isProcessing -> {
                    // Show processing state
                    ProcessingScreen()
                }
                else -> {
                    // Show main recording interface
                    MainRecordingScreen(
                        isRecording = isRecording,
                        onRecordingToggle = {
                            if (isRecording) {
                                onStopRecording()
                                isRecording = false
                                isProcessing = true
                                // Simulate processing delay
                                // TODO: Replace with actual phone communication
                            } else {
                                onStartRecording()
                                isRecording = true
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun MainRecordingScreen(
    isRecording: Boolean,
    onRecordingToggle: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = Modifier.fillMaxSize()
    ) {
        // App title
        Text(
            text = "AI Emergency",
            style = MaterialTheme.typography.title3,
            color = MaterialTheme.colors.primary,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Recording button
        Button(
            onClick = onRecordingToggle,
            modifier = Modifier.size(80.dp),
            colors = ButtonDefaults.buttonColors(
                backgroundColor = if (isRecording) Color.Red else MaterialTheme.colors.primary
            ),
            shape = CircleShape
        ) {
            Icon(
                imageVector = if (isRecording) Icons.Default.Stop else Icons.Default.Mic,
                contentDescription = if (isRecording) "Stop Recording" else "Start Recording",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Status text
        Text(
            text = if (isRecording) "Recording..." else "Tap to Record",
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.onBackground,
            textAlign = TextAlign.Center
        )

        if (isRecording) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Describe your emergency",
                style = MaterialTheme.typography.caption1,
                color = MaterialTheme.colors.onBackground.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun ProcessingScreen() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = Modifier.fillMaxSize()
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(40.dp),
            color = MaterialTheme.colors.primary
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Analyzing...",
            style = MaterialTheme.typography.body1,
            color = MaterialTheme.colors.primary,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Generating emergency SMS",
            style = MaterialTheme.typography.caption1,
            color = MaterialTheme.colors.onBackground.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun SmsResultScreen(
    sms: String,
    onBack: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Emergency SMS",
            style = MaterialTheme.typography.title3,
            color = MaterialTheme.colors.primary,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            backgroundColor = MaterialTheme.colors.surface
        ) {
            Text(
                text = sms,
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.padding(12.dp),
                textAlign = TextAlign.Start
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Button(
            onClick = onBack,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                backgroundColor = MaterialTheme.colors.primary
            )
        ) {
            Text(
                text = "New Emergency",
                color = Color.White,
                style = MaterialTheme.typography.button
            )
        }
    }
}

@Preview(device = WearDevices.SMALL_ROUND, showSystemUi = true)
@Composable
fun MainScreenPreview() {
    EmergencyWearApp(
        onStartRecording = {},
        onStopRecording = {}
    )
}

@Preview(device = WearDevices.SMALL_ROUND, showSystemUi = true)
@Composable
fun ProcessingScreenPreview() {
    Ai_comp_wearableosTheme {
        ProcessingScreen()
    }
}