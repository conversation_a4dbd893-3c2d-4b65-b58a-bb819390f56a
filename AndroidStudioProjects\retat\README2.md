# RETAT: AI Emergency Companion Technical Documentation

## 📱 Project Overview

RETAT (Real-time Emergency Text and Assistance Tool) is comprehensive emergency assistance system consisting of two main components:

1. **Main Phone Application (Flutter/kotlin)**: A full-featured emergency companion app that uses on-device AI to analyze emergency situations and generate appropriate SMS messages and guidance.

2. **Wear OS Companion App (Kotlin)**: A voice-focused smartwatch application that allows users to quickly record emergency descriptions and receive AI-generated SMS responses from the main app.

This document provides a detailed technical overview of both applications, their architecture, communication flow, and key features.

## 🏗️ System Architecture

### Main Application (Phone)

#### Technology Stack
- **Frontend**: Flutter/Dart
- **State Management**: Riverpod
- **Native Integration**: Method Channels
- **AI Processing**: MediaPipe Tasks (Gemma 3n-E2B-it-int4 model)
- **Voice Recognition**: VOSK (offline speech-to-text)
- **Location Services**: Native Android GPS integration

#### Project Structure
```
retat/
├── lib/
│   ├── main.dart                 # Application entry point
│   ├── models/                   # Data models
│   │   └── ai_response.dart      # AI response structure
│   ├── providers/                # Riverpod state providers
│   │   └── home_provider.dart    # Main app state management
│   ├── screens/                  # UI screens
│   │   ├── home_screen.dart      # Main interface
│   │   └── model_loading_screen.dart # Initial loading screen
│   ├── services/                 # Business logic
│   │   ├── ai_service.dart       # AI inference handling
│   │   └── location_service.dart # GPS functionality
│   └── utils/                    # Utilities
│       └── app_constants.dart    # Application constants
├── android/                      # Native Android code
│   └── app/src/main/kotlin/
│       └── com/example/myapp/
│           ├── MainActivity.kt   # Native method implementations
│           └── WearDataListenerService.kt # Watch communication
```

#### Native Components
- **LLM Inference**: Gemma 3n-E2B-it-int4 model for on-device AI processing
- **VOSK Integration**: Offline speech-to-text processing
- **Location Services**: Native GPS access via Method Channels
- **Wear OS Communication**: Data Layer API for watch connectivity

### Wear OS Application (Watch)

#### Technology Stack
- **Frontend**: Jetpack Compose for Wear OS
- **Language**: Kotlin
- **Communication**: Wear OS Data Layer API
- **Audio Recording**: MediaRecorder
- **UI Framework**: Material Design for Wear OS
#### Project Structure
```
ai_comp_wearableos/
├── app/src/main/
│   ├── java/com/example/ai_comp_wearableos/
│   │   ├── presentation/
│   │   │   └── MainActivity.kt   # Main activity and UI components
│   │   └── services/
│   │       └── PhoneCommunicationService.kt # Phone connectivity
│   ├── res/                      # Resources
│   └── AndroidManifest.xml       # App configuration
├── gradle/                       # Build configuration
└── build.gradle.kts              # Dependencies
```

## 🔄 Communication Flow

### End-to-End Emergency Processing

1. **User Input (Phone or Watch)**
   - Text input directly on phone
   - Voice recording on phone or watch
   - Optional image capture on phone

2. **Voice Processing**
   - Audio recorded as .3gp file
   - VOSK transcribes audio to text
   - Transcribed text used as input for AI

3. **AI Processing**
   - Gemma model loaded on device
   - Prompt constructed with input and location
   - Model generates structured response

4. **Response Generation**
   - SMS draft with emergency details
   - Step-by-step guidance instructions
   - Location coordinates embedded in SMS

5. **Output Display**
   - Phone: SMS draft + guidance steps
   - Watch: SMS draft only

### Watch-to-Phone Communication

```
Watch App → Data Layer API → Phone App → AI Processing → SMS Response → Watch Display
```

1. **Voice Recording (Watch)**
   - User taps microphone button
   - Audio recorded using MediaRecorder
   - Saved as temporary file

2. **Data Transmission**
   - Audio file converted to Asset
   - Sent via Data Layer API
   - High priority flag for emergency

3. **Phone Processing**
   - WearableListenerService receives data
   - Audio transcribed with VOSK
   - AI processes with isFromWatch=true flag

4. **Response Return**
   - SMS-only response generated
   - Sent back via Data Layer API
   - Watch displays result

## 🧠 AI Implementation

### Model Details
- **Model**: Gemma 3n-E2B-it-int4 (quantized)
- **Framework**: MediaPipe Tasks for on-device inference
- **Prompt Engineering**: Structured prompts for emergency scenarios
- **Output Format**: JSON-like structure with SMS and guidance steps

### Prompt Structure
```
You are an emergency assistant. Given the following emergency report:
[USER_INPUT]

Location: [LATITUDE], [LONGITUDE]

Generate:
1. An SMS draft to send to emergency services
2. Step-by-step guidance for the person in the emergency

Format your response exactly like this:
SMS: [emergency SMS with location and key details]
GUIDANCE:
- [Step 1]
- [Step 2]
- [Step 3]
```

### Watch-Specific Processing
- Simplified prompt for watch inputs
- SMS-only response (no guidance steps)
- Location from phone used in SMS

## 📊 Key Features

### Main Phone App
- **Multi-modal Input**: Text, voice, and image input options
- **Real-time Location**: GPS coordinates for emergency services
- **On-device AI**: Privacy-focused processing without cloud dependency
- **Offline Operation**: Functions without internet connection
- **Step-by-step Guidance**: Contextual emergency instructions
- **SMS Generation**: Ready-to-send emergency messages

### Wear OS Companion
- **Voice-first Interface**: Simple tap-to-record functionality
- **Responsive Design**: Adapts to different watch form factors
- **Connection Status**: Visual indicators for phone connectivity
- **Error Handling**: Clear feedback for communication issues
- **Animated UI**: Professional emergency-focused interface
- **Low-latency**: Quick response for emergency situations

## 🔧 Technical Implementation Details

### Voice Processing
```kotlin
// Phone-side transcription (VOSK)
private fun transcribeWavWithVosk(context: Context, wavPath: String): String {
    try {
        val model = Model(File(context.filesDir, "vosk-model-small-en-us-0.15"))
        val recognizer = Recognizer(model, 16000.0f)
        
        // Process audio file
        val buffer = ByteArray(4096)
        val inputStream = FileInputStream(wavPath)
        
        while (true) {
            val nread = inputStream.read(buffer)
            if (nread == -1) break
            recognizer.acceptWaveForm(buffer, nread)
        }
        
        // Get final result
        val result = recognizer.finalResult
        val jsonResult = JSONObject(result)
        return jsonResult.getString("text")
    } catch (e: Exception) {
        printLog("Error in transcribeWavWithVosk: ${e.message}")
        return ""
    }
}
```

### AI Inference
```dart
Future<AiResponse> runInference(String prompt, {
  String? imagePath,
  double? latitude,
  double? longitude,
  bool isFromWatch = false,
}) async {
  // Build the real prompt with template
  final realPrompt = buildPrompt(
    userInput: prompt,
    latitude: latitude ?? 0.0,
    longitude: longitude ?? 0.0,
    hasImage: imagePath != null,
    isFromWatch: isFromWatch,
  );

  // Call native code for inference
  final args = <String, dynamic>{'text': realPrompt};
  if (imagePath != null) args['imagePath'] = imagePath;
  
  final response = await _channel.invokeMethod('runLlmInference', args);
  final output = response as String;
  
  // Parse structured output
  final parsed = parseModelOutput(output);
  return AiResponse(
    smsDraft: parsed['sms'] ?? '',
    guidanceSteps: List<String>.from(parsed['guideSteps'] ?? []),
  );
}
```

### Watch-Phone Communication
```kotlin
// Watch side: Send voice to phone
suspend fun sendVoiceToPhone(audioFile: File): Boolean {
    return withContext(Dispatchers.IO) {
        try {
            // Create asset from audio file
            val asset = Asset.createFromBytes(audioFile.readBytes())
            
            // Create data request
            val putDataRequest = PutDataRequest.create(EMERGENCY_VOICE_PATH).apply {
                val dataMap = DataMap().apply {
                    putAsset("audio_file", asset)
                    putLong("timestamp", System.currentTimeMillis())
                }
                setData(dataMap.toByteArray())
                setUrgent() // High priority for emergency
            }
            
            // Send to phone
            val result = dataClient.putDataItem(putDataRequest).await()
            true
        } catch (e: Exception) {
            onError?.invoke("Failed to send voice: ${e.message}")
            false
        }
    }
}

// Phone side: Receive and process voice
override fun onDataChanged(dataEvents: DataEventBuffer) {
    for (event in dataEvents) {
        if (event.type == DataEvent.TYPE_CHANGED) {
            val dataItem = event.dataItem
            when (dataItem.uri.path) {
                EMERGENCY_VOICE_PATH -> {
                    handleEmergencyVoice(dataItem)
                }
            }
        }
    }
}
```

## 🔍 Dependencies

### Main App (Flutter)
- **flutter_riverpod**: State management
- **speech_to_text**: Voice input
- **image_picker**: Image capture
- **permission_handler**: Permission management
- **record**: Audio recording
- **just_audio**: Audio playback
- **path_provider**: File system access
- **tflite_flutter**: TensorFlow Lite integration

### Native Android (Main App)
- **MediaPipe Tasks**: AI model execution
- **VOSK**: Offline speech recognition
- **Wear OS Data Layer**: Watch communication

### Wear OS App
- **Jetpack Compose for Wear**: UI framework
- **Wear OS Data Layer API**: Phone communication
- **Kotlin Coroutines**: Asynchronous operations
- **MediaRecorder**: Voice recording



*This technical documentation provides a comprehensive overview of the RETAT system architecture, communication flow, and implementation details for both the main phone application and Wear OS companion app.*
