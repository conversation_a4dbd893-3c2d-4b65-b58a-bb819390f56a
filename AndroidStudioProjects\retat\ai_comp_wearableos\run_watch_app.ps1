Write-Host "🎯 Building and running Enhanced AI Emergency Watch App..." -ForegroundColor Cyan
Write-Host ""

Write-Host "📦 Step 1: Building the watch app..." -ForegroundColor Yellow
& .\gradlew assembleDebug
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "📱 Step 2: Installing on emulator-5554..." -ForegroundColor Yellow
& adb -s emulator-5554 install -r app\build\outputs\apk\debug\app-debug.apk
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Installation failed! Make sure emulator-5554 is running." -ForegroundColor Red
    Write-Host "💡 Try: Check if the Wear OS emulator is started and accessible" -ForegroundColor Blue
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "🚀 Step 3: Launching the app..." -ForegroundColor Yellow
& adb -s emulator-5554 shell am start -n com.example.ai_comp_wearableos/.presentation.MainActivity
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Launch failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "✅ Enhanced AI Emergency Watch App launched successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🎨 NEW FEATURES:" -ForegroundColor Magenta
Write-Host "  ✨ Dynamic animations and transitions" -ForegroundColor White
Write-Host "  📱 Watch-optimized responsive design" -ForegroundColor White
Write-Host "  🎯 Emergency-focused gradient backgrounds" -ForegroundColor White
Write-Host "  💫 Pulsing recording animations" -ForegroundColor White
Write-Host "  📊 Processing progress indicators" -ForegroundColor White
Write-Host "  🎪 Smooth screen transitions" -ForegroundColor White
Write-Host ""
Write-Host "🎤 How to test the enhanced UX:" -ForegroundColor Cyan
Write-Host "  • Notice the gradient background and emergency icon" -ForegroundColor White
Write-Host "  • Tap the animated microphone button" -ForegroundColor White
Write-Host "  • Watch the pulsing red recording animation" -ForegroundColor White
Write-Host "  • See the smooth transition to processing screen" -ForegroundColor White
Write-Host "  • Experience the animated progress indicators" -ForegroundColor White
Write-Host "  • View the polished SMS result screen" -ForegroundColor White
Write-Host ""
Write-Host "📐 Responsive Design Features:" -ForegroundColor Yellow
Write-Host "  • Auto-adjusts for round/square watch faces" -ForegroundColor Gray
Write-Host "  • Dynamic sizing based on screen dimensions" -ForegroundColor Gray
Write-Host "  • Optimized touch targets for emergency use" -ForegroundColor Gray
Write-Host "  • High contrast colors for visibility" -ForegroundColor Gray
Write-Host ""
Write-Host "🔧 Ready for integration:" -ForegroundColor Yellow
Write-Host "  • Data Layer API for phone communication" -ForegroundColor Gray
Write-Host "  • Voice-to-text processing" -ForegroundColor Gray
Write-Host "  • Real AI emergency analysis" -ForegroundColor Gray
Write-Host ""
Read-Host "Press Enter to exit"
