import 'package:flutter/material.dart';
import '../services/model_downloader.dart';
import 'home_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ModelLoadingScreen extends StatefulWidget {
  const ModelLoadingScreen({super.key});

  @override
  State<ModelLoadingScreen> createState() => _ModelLoadingScreenState();
}

class _ModelLoadingScreenState extends State<ModelLoadingScreen> {
  double _progress = 0.0;
  String _status = 'Checking model...';
  bool _checking = true;
  bool _downloading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkFirstLaunch();
  }

  Future<void> _checkFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final modelReady = prefs.getBool('model_ready') ?? false;

      print('[ModelLoadingScreen] Model ready flag: $modelReady');

      if (modelReady) {
        // Even if marked as ready, double-check the model exists
        setState(() {
          _status = 'Verifying model...';
        });

        final modelExists = await ModelDownloader.modelIsComplete();
        if (modelExists) {
          print('[ModelLoadingScreen] Model verified, going to home');
          _goToHome();
        } else {
          print(
              '[ModelLoadingScreen] Model missing despite ready flag, re-downloading');
          await prefs.setBool('model_ready', false);
          _checkAndDownloadModel(prefs);
        }
      } else {
        print('[ModelLoadingScreen] Model not ready, checking and downloading');
        _checkAndDownloadModel(prefs);
      }
    } catch (e) {
      print('[ModelLoadingScreen] Error in _checkFirstLaunch: $e');
      setState(() {
        _errorMessage = 'Failed to initialize: $e';
        _checking = false;
        _downloading = false;
      });
    }
  }

  Future<void> _checkAndDownloadModel([SharedPreferences? prefs]) async {
    setState(() {
      _checking = true;
      _progress = 0.0;
      _status = 'Checking model...';
      _errorMessage = null;
    });

    try {
      print('[ModelLoadingScreen] Checking if model is complete...');
      final complete = await ModelDownloader.modelIsComplete();

      if (complete) {
        print('[ModelLoadingScreen] Model is complete, setting ready flag');
        await _setModelReady(prefs);
        _goToHome();
        return;
      }

      print(
          '[ModelLoadingScreen] Model not complete, checking internet connection...');
      setState(() {
        _status = 'Checking internet connection...';
      });

      final hasInternet = await ModelDownloader.hasInternetConnection();
      if (!hasInternet) {
        throw Exception(
            'No internet connection available. Please check your network settings and try again.');
      }

      print(
          '[ModelLoadingScreen] Internet connection confirmed, starting download...');
      setState(() {
        _checking = false;
        _downloading = true;
        _status = 'Downloading AI model... This may take several minutes.';
      });

      await ModelDownloader.downloadModel(onProgress: (p) {
        setState(() {
          _progress = p;
          _status = 'Downloading AI model... ${(p * 100).toStringAsFixed(1)}%';
        });
      });

      print('[ModelLoadingScreen] Download completed, setting ready flag');
      await _setModelReady(prefs);
      _goToHome();
    } catch (e) {
      print('[ModelLoadingScreen] Error in _checkAndDownloadModel: $e');
      setState(() {
        _downloading = false;
        _checking = false;
        _errorMessage = e.toString();
      });
      // _showErrorDialog(e.toString());
    }
  }

  Future<void> _setModelReady([SharedPreferences? prefs]) async {
    final prefs0 = prefs ?? await SharedPreferences.getInstance();
    await prefs0.setBool('model_ready', true);
  }

  void _goToHome() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const HomeScreen()),
    );
  }

  // void _showErrorDialog(String message) {
  //   showDialog(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (context) => AlertDialog(
  //       title: Row(
  //         children: const [
  //           Icon(Icons.error_outline, color: Colors.red),
  //           SizedBox(width: 8),
  //           Text('Download Error'),
  //         ],
  //       ),
  //       content: Text(message),
  //       actions: [
  //         TextButton(
  //           onPressed: () {
  //             Navigator.of(context).pop();
  //             _checkAndDownloadModel();
  //           },
  //           child: const Text('Retry'),
  //         ),
  //         TextButton(
  //           onPressed: () => Navigator.of(context).pop(),
  //           child: const Text('Cancel'),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  void _showManualInstructions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue),
            SizedBox(width: 8),
            Text('Manual Model Setup'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'The automatic download requires authentication. You can manually download and place the model file:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('1. Visit one of these URLs:'),
              const SizedBox(height: 8),
              const SelectableText(
                '• https://huggingface.co/litert-community/Gemma3-1B-IT\n'
                '• https://huggingface.co/google/gemma-3n-E2B-it-litert-preview',
                style: TextStyle(fontFamily: 'monospace', fontSize: 12),
              ),
              const SizedBox(height: 16),
              const Text(
                  '2. Create a Hugging Face account and accept the license terms'),
              const SizedBox(height: 8),
              const Text('3. Download the .task file'),
              const SizedBox(height: 8),
              const Text(
                  '4. Place it in one of these locations on your device:'),
              const SizedBox(height: 8),
              const SelectableText(
                '• /sdcard/Download/\n'
                '• /storage/emulated/0/Android/data/com.example.myapp/files/',
                style: TextStyle(fontFamily: 'monospace', fontSize: 12),
              ),
              const SizedBox(height: 16),
              const Text('5. Restart the app'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Note: The model file is approximately 1-2 GB in size.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _checkAndDownloadModel();
            },
            child: const Text('Check Again'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE3F2FD),
              Color(0xFFF3E5F5),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.smart_toy,
                      size: 48,
                      color: Color(0xFF3F51B5),
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Preparing AI Model',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1A237E),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Setting up your emergency AI companion',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF5C6BC0),
                    ),
                  ),
                  const SizedBox(height: 32),
                  if (_errorMessage != null) ...[
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Column(
                        children: [
                          const Icon(Icons.error, color: Colors.red, size: 32),
                          const SizedBox(height: 8),
                          const Text(
                            'Setup Failed',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              ElevatedButton(
                                onPressed: () => _checkAndDownloadModel(),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Retry'),
                              ),
                              ElevatedButton(
                                onPressed: _showManualInstructions,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Manual Setup'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ] else if (_checking) ...[
                    const CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF3F51B5)),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _status,
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    ),
                  ] else if (_downloading) ...[
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          LinearProgressIndicator(
                            value: _progress,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                Color(0xFF3F51B5)),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            '${(_progress * 100).toStringAsFixed(1)}%',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF3F51B5),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _status,
                            style: const TextStyle(fontSize: 14),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Please keep the app open during download',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
