package com.example.myapp

import android.util.Log
import com.google.android.gms.wearable.*
import kotlinx.coroutines.*
import kotlinx.coroutines.tasks.await
import java.io.File
import java.io.FileOutputStream

class WearDataListenerService : WearableListenerService() {
    
    companion object {
        private const val TAG = "WearDataListener"
        private const val EMERGENCY_VOICE_PATH = "/emergency_voice"
        private const val SMS_RESPONSE_PATH = "/sms_response"
        private const val REQUEST_SMS_PATH = "/request_sms"
        private const val CONNECTION_CHECK_PATH = "/connection_check"
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    override fun onDataChanged(dataEvents: DataEventBuffer) {
        super.onDataChanged(dataEvents)
        
        for (event in dataEvents) {
            if (event.type == DataEvent.TYPE_CHANGED) {
                val dataItem = event.dataItem
                Log.d(TAG, "Data changed: ${dataItem.uri.path}")
                
                when (dataItem.uri.path) {
                    EMERGENCY_VOICE_PATH -> {
                        handleEmergencyVoice(dataItem)
                    }
                    REQUEST_SMS_PATH -> {
                        handleEmergencySmsRequest(dataItem)
                    }
                }
            }
        }
    }
    
    override fun onMessageReceived(messageEvent: MessageEvent) {
        super.onMessageReceived(messageEvent)
        
        when (messageEvent.path) {
            CONNECTION_CHECK_PATH -> {
                Log.d(TAG, "Connection check received from watch")
                // Respond to connection check
                serviceScope.launch {
                    sendConnectionResponse(messageEvent.sourceNodeId)
                }
            }
        }
    }
    
    private fun handleEmergencyVoice(dataItem: DataItem) {
        serviceScope.launch {
            try {
                Log.d(TAG, "Processing emergency voice from watch")
                
                val dataMap = DataMapItem.fromDataItem(dataItem).dataMap
                val asset = dataMap.getAsset("audio_file")
                
                if (asset != null) {
                    // Get audio file from watch
                    val audioFile = getAudioFileFromAsset(asset)
                    
                    if (audioFile != null) {
                        // Process with AI (watch mode)
                        processEmergencyAudio(audioFile)
                    } else {
                        sendErrorToWatch("Failed to receive audio file")
                    }
                } else {
                    sendErrorToWatch("No audio file in request")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling emergency voice", e)
                sendErrorToWatch("Processing error: ${e.message}")
            }
        }
    }
    
    private fun handleEmergencySmsRequest(dataItem: DataItem) {
        serviceScope.launch {
            try {
                Log.d(TAG, "Processing emergency SMS request from watch")
                
                val dataMap = DataMapItem.fromDataItem(dataItem).dataMap
                val voiceText = String(dataMap.getByteArray("voice_text") ?: byteArrayOf())
                
                if (voiceText.isNotEmpty()) {
                    // Process with AI (watch mode - text only)
                    processEmergencyText(voiceText)
                } else {
                    sendErrorToWatch("No voice text in request")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling SMS request", e)
                sendErrorToWatch("SMS processing error: ${e.message}")
            }
        }
    }
    
    private suspend fun getAudioFileFromAsset(asset: Asset): File? {
        return withContext(Dispatchers.IO) {
            try {
                val inputStream = Wearable.getDataClient(this@WearDataListenerService)
                    .getFdForAsset(asset).await().inputStream
                
                val audioFile = File(cacheDir, "watch_emergency_${System.currentTimeMillis()}.3gp")
                val outputStream = FileOutputStream(audioFile)
                
                inputStream.use { input ->
                    outputStream.use { output ->
                        input.copyTo(output)
                    }
                }
                
                Log.d(TAG, "Audio file saved: ${audioFile.absolutePath}")
                audioFile
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get audio file from asset", e)
                null
            }
        }
    }
    
    private suspend fun processEmergencyAudio(audioFile: File) {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Processing emergency audio with AI...")
                
                // Use VOSK to transcribe audio
                val transcription = transcribeAudioWithVosk(audioFile)
                
                if (transcription.isNotEmpty()) {
                    Log.d(TAG, "Audio transcribed: $transcription")
                    processEmergencyText(transcription)
                } else {
                    sendErrorToWatch("Failed to transcribe audio")
                }
                
                // Clean up audio file
                audioFile.delete()
            } catch (e: Exception) {
                Log.e(TAG, "Error processing emergency audio", e)
                sendErrorToWatch("Audio processing error: ${e.message}")
            }
        }
    }
    
    private suspend fun processEmergencyText(emergencyText: String) {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Processing emergency text with AI: $emergencyText")
                
                // Call the existing AI processing method with watch flag
                val result = processWatchEmergency(emergencyText)
                
                if (result.isNotEmpty()) {
                    sendSmsToWatch(result)
                } else {
                    sendErrorToWatch("AI processing failed")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing emergency text", e)
                sendErrorToWatch("AI processing error: ${e.message}")
            }
        }
    }
    
    private suspend fun sendSmsToWatch(smsText: String) {
        withContext(Dispatchers.IO) {
            try {
                val putDataRequest = PutDataRequest.create(SMS_RESPONSE_PATH).apply {
                    val dataMap = DataMap().apply {
                        putString("sms_text", smsText)
                        putBoolean("success", true)
                        putLong("timestamp", System.currentTimeMillis())
                    }
                    setData(dataMap.toByteArray())
                    setUrgent()
                }
                
                Wearable.getDataClient(this@WearDataListenerService)
                    .putDataItem(putDataRequest).await()
                
                Log.d(TAG, "SMS sent to watch: $smsText")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send SMS to watch", e)
            }
        }
    }
    
    private suspend fun sendErrorToWatch(error: String) {
        withContext(Dispatchers.IO) {
            try {
                val putDataRequest = PutDataRequest.create(SMS_RESPONSE_PATH).apply {
                    val dataMap = DataMap().apply {
                        putString("error", error)
                        putBoolean("success", false)
                        putLong("timestamp", System.currentTimeMillis())
                    }
                    setData(dataMap.toByteArray())
                    setUrgent()
                }
                
                Wearable.getDataClient(this@WearDataListenerService)
                    .putDataItem(putDataRequest).await()
                
                Log.d(TAG, "Error sent to watch: $error")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send error to watch", e)
            }
        }
    }
    
    private suspend fun sendConnectionResponse(nodeId: String) {
        withContext(Dispatchers.IO) {
            try {
                Wearable.getMessageClient(this@WearDataListenerService)
                    .sendMessage(nodeId, CONNECTION_CHECK_PATH, "pong".toByteArray()).await()
                
                Log.d(TAG, "Connection response sent to watch")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send connection response", e)
            }
        }
    }
    
    // Placeholder methods - these will be implemented to call existing MainActivity methods
    private fun transcribeAudioWithVosk(audioFile: File): String {
        // TODO: Call MainActivity.transcribeWavWithVosk
        return "Emergency audio transcription placeholder"
    }
    
    private fun processWatchEmergency(text: String): String {
        // TODO: Call MainActivity AI processing with isFromWatch = true
        return "🚨 EMERGENCY: $text. Location needed. Send help immediately."
    }
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
    }
}
