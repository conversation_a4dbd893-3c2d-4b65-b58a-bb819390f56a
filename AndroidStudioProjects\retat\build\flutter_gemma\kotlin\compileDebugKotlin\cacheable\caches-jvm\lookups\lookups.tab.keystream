  Context android.content  Bitmap android.graphics  
BitmapFactory android.graphics  decodeByteArray android.graphics.BitmapFactory  Log android.util  getStackTraceString android.util.Log  BitmapImageBuilder $com.google.mediapipe.framework.image  build 7com.google.mediapipe.framework.image.BitmapImageBuilder  GraphOptions -com.google.mediapipe.tasks.genai.llminference  LlmInference -com.google.mediapipe.tasks.genai.llminference  LlmInferenceSession -com.google.mediapipe.tasks.genai.llminference  ProgressListener -com.google.mediapipe.tasks.genai.llminference  builder :com.google.mediapipe.tasks.genai.llminference.GraphOptions  build Bcom.google.mediapipe.tasks.genai.llminference.GraphOptions.Builder  setEnableVisionModality Bcom.google.mediapipe.tasks.genai.llminference.GraphOptions.Builder  Backend :com.google.mediapipe.tasks.genai.llminference.LlmInference  close :com.google.mediapipe.tasks.genai.llminference.LlmInference  createFromOptions :com.google.mediapipe.tasks.genai.llminference.LlmInference  values Bcom.google.mediapipe.tasks.genai.llminference.LlmInference.Backend  Builder Ncom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions  builder Ncom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions  build Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setMaxNumImages Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setMaxTokens Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setModelPath Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setPreferredBackend Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  setSupportedLoraRanks Vcom.google.mediapipe.tasks.genai.llminference.LlmInference.LlmInferenceOptions.Builder  addImage Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  
addQueryChunk Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  close Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  createFromOptions Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  generateResponse Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  generateResponseAsync Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  sizeInTokens Acom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession  Builder \com.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions  builder \com.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions  build dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setGraphOptions dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setLoraPath dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  
setRandomSeed dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setTemperature dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setTopK dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  setTopP dcom.google.mediapipe.tasks.genai.llminference.LlmInferenceSession.LlmInferenceSessionOptions.Builder  <SAM-CONSTRUCTOR> >com.google.mediapipe.tasks.genai.llminference.ProgressListener  Any dev.flutterberlin.flutter_gemma  BasicMessageChannel dev.flutterberlin.flutter_gemma  BinaryMessenger dev.flutterberlin.flutter_gemma  
BitmapFactory dev.flutterberlin.flutter_gemma  BitmapImageBuilder dev.flutterberlin.flutter_gemma  Boolean dev.flutterberlin.flutter_gemma  BufferOverflow dev.flutterberlin.flutter_gemma  Byte dev.flutterberlin.flutter_gemma  	ByteArray dev.flutterberlin.flutter_gemma  ByteArrayOutputStream dev.flutterberlin.flutter_gemma  
ByteBuffer dev.flutterberlin.flutter_gemma  Context dev.flutterberlin.flutter_gemma  CoroutineScope dev.flutterberlin.flutter_gemma  Dispatchers dev.flutterberlin.flutter_gemma  Double dev.flutterberlin.flutter_gemma  EventChannel dev.flutterberlin.flutter_gemma  	Exception dev.flutterberlin.flutter_gemma  File dev.flutterberlin.flutter_gemma  Float dev.flutterberlin.flutter_gemma  FlutterError dev.flutterberlin.flutter_gemma  FlutterGemmaPlugin dev.flutterberlin.flutter_gemma  
FlutterPlugin dev.flutterberlin.flutter_gemma  GraphOptions dev.flutterberlin.flutter_gemma  IllegalArgumentException dev.flutterberlin.flutter_gemma  IllegalStateException dev.flutterberlin.flutter_gemma  InferenceModel dev.flutterberlin.flutter_gemma  InferenceModelConfig dev.flutterberlin.flutter_gemma  InferenceModelSession dev.flutterberlin.flutter_gemma  InferenceSessionConfig dev.flutterberlin.flutter_gemma  Int dev.flutterberlin.flutter_gemma  JvmOverloads dev.flutterberlin.flutter_gemma  List dev.flutterberlin.flutter_gemma  LlmInference dev.flutterberlin.flutter_gemma  LlmInferenceSession dev.flutterberlin.flutter_gemma  Log dev.flutterberlin.flutter_gemma  Long dev.flutterberlin.flutter_gemma  MessageCodec dev.flutterberlin.flutter_gemma  MutableSharedFlow dev.flutterberlin.flutter_gemma  Pair dev.flutterberlin.flutter_gemma  PigeonInterfacePigeonCodec dev.flutterberlin.flutter_gemma  PlatformService dev.flutterberlin.flutter_gemma  PlatformServiceImpl dev.flutterberlin.flutter_gemma  PreferredBackend dev.flutterberlin.flutter_gemma  PreferredBackendEnum dev.flutterberlin.flutter_gemma  Result dev.flutterberlin.flutter_gemma  RuntimeException dev.flutterberlin.flutter_gemma  
SharedFlow dev.flutterberlin.flutter_gemma  StandardMessageCodec dev.flutterberlin.flutter_gemma  String dev.flutterberlin.flutter_gemma  Suppress dev.flutterberlin.flutter_gemma  	Throwable dev.flutterberlin.flutter_gemma  Unit dev.flutterberlin.flutter_gemma  apply dev.flutterberlin.flutter_gemma  asSharedFlow dev.flutterberlin.flutter_gemma  config dev.flutterberlin.flutter_gemma  context dev.flutterberlin.flutter_gemma  failure dev.flutterberlin.flutter_gemma  firstOrNull dev.flutterberlin.flutter_gemma  	getOrNull dev.flutterberlin.flutter_gemma  getValue dev.flutterberlin.flutter_gemma  inferenceModel dev.flutterberlin.flutter_gemma  
isNotEmpty dev.flutterberlin.flutter_gemma  	javaClass dev.flutterberlin.flutter_gemma  launch dev.flutterberlin.flutter_gemma  lazy dev.flutterberlin.flutter_gemma  let dev.flutterberlin.flutter_gemma  listOf dev.flutterberlin.flutter_gemma  map dev.flutterberlin.flutter_gemma  mapOf dev.flutterberlin.flutter_gemma  ofRaw dev.flutterberlin.flutter_gemma  provideDelegate dev.flutterberlin.flutter_gemma  run dev.flutterberlin.flutter_gemma  session dev.flutterberlin.flutter_gemma  setUp dev.flutterberlin.flutter_gemma  success dev.flutterberlin.flutter_gemma  to dev.flutterberlin.flutter_gemma  values dev.flutterberlin.flutter_gemma  withContext dev.flutterberlin.flutter_gemma  	wrapError dev.flutterberlin.flutter_gemma  
wrapResult dev.flutterberlin.flutter_gemma  	EventSink ,dev.flutterberlin.flutter_gemma.EventChannel  
StreamHandler ,dev.flutterberlin.flutter_gemma.EventChannel  code ,dev.flutterberlin.flutter_gemma.FlutterError  details ,dev.flutterberlin.flutter_gemma.FlutterError  message ,dev.flutterberlin.flutter_gemma.FlutterError  EventChannel 2dev.flutterberlin.flutter_gemma.FlutterGemmaPlugin  PlatformService 2dev.flutterberlin.flutter_gemma.FlutterGemmaPlugin  PlatformServiceImpl 2dev.flutterberlin.flutter_gemma.FlutterGemmaPlugin  eventChannel 2dev.flutterberlin.flutter_gemma.FlutterGemmaPlugin  setUp 2dev.flutterberlin.flutter_gemma.FlutterGemmaPlugin  FlutterPluginBinding -dev.flutterberlin.flutter_gemma.FlutterPlugin  BufferOverflow .dev.flutterberlin.flutter_gemma.InferenceModel  File .dev.flutterberlin.flutter_gemma.InferenceModel  IllegalArgumentException .dev.flutterberlin.flutter_gemma.InferenceModel  InferenceModelSession .dev.flutterberlin.flutter_gemma.InferenceModel  LlmInference .dev.flutterberlin.flutter_gemma.InferenceModel  MutableSharedFlow .dev.flutterberlin.flutter_gemma.InferenceModel  RuntimeException .dev.flutterberlin.flutter_gemma.InferenceModel  _errors .dev.flutterberlin.flutter_gemma.InferenceModel  _partialResults .dev.flutterberlin.flutter_gemma.InferenceModel  apply .dev.flutterberlin.flutter_gemma.InferenceModel  asSharedFlow .dev.flutterberlin.flutter_gemma.InferenceModel  close .dev.flutterberlin.flutter_gemma.InferenceModel  config .dev.flutterberlin.flutter_gemma.InferenceModel  
createSession .dev.flutterberlin.flutter_gemma.InferenceModel  errors .dev.flutterberlin.flutter_gemma.InferenceModel  	getOrNull .dev.flutterberlin.flutter_gemma.InferenceModel  let .dev.flutterberlin.flutter_gemma.InferenceModel  llmInference .dev.flutterberlin.flutter_gemma.InferenceModel  modelExists .dev.flutterberlin.flutter_gemma.InferenceModel  partialResults .dev.flutterberlin.flutter_gemma.InferenceModel  maxNumImages 4dev.flutterberlin.flutter_gemma.InferenceModelConfig  	maxTokens 4dev.flutterberlin.flutter_gemma.InferenceModelConfig  	modelPath 4dev.flutterberlin.flutter_gemma.InferenceModelConfig  preferredBackend 4dev.flutterberlin.flutter_gemma.InferenceModelConfig  supportedLoraRanks 4dev.flutterberlin.flutter_gemma.InferenceModelConfig  
BitmapFactory 5dev.flutterberlin.flutter_gemma.InferenceModelSession  BitmapImageBuilder 5dev.flutterberlin.flutter_gemma.InferenceModelSession  GraphOptions 5dev.flutterberlin.flutter_gemma.InferenceModelSession  IllegalArgumentException 5dev.flutterberlin.flutter_gemma.InferenceModelSession  LlmInferenceSession 5dev.flutterberlin.flutter_gemma.InferenceModelSession  addImage 5dev.flutterberlin.flutter_gemma.InferenceModelSession  
addQueryChunk 5dev.flutterberlin.flutter_gemma.InferenceModelSession  apply 5dev.flutterberlin.flutter_gemma.InferenceModelSession  close 5dev.flutterberlin.flutter_gemma.InferenceModelSession  config 5dev.flutterberlin.flutter_gemma.InferenceModelSession  generateResponse 5dev.flutterberlin.flutter_gemma.InferenceModelSession  generateResponseAsync 5dev.flutterberlin.flutter_gemma.InferenceModelSession  let 5dev.flutterberlin.flutter_gemma.InferenceModelSession  llmInference 5dev.flutterberlin.flutter_gemma.InferenceModelSession  
resultFlow 5dev.flutterberlin.flutter_gemma.InferenceModelSession  session 5dev.flutterberlin.flutter_gemma.InferenceModelSession  sizeInTokens 5dev.flutterberlin.flutter_gemma.InferenceModelSession  to 5dev.flutterberlin.flutter_gemma.InferenceModelSession  enableVisionModality 6dev.flutterberlin.flutter_gemma.InferenceSessionConfig  loraPath 6dev.flutterberlin.flutter_gemma.InferenceSessionConfig  
randomSeed 6dev.flutterberlin.flutter_gemma.InferenceSessionConfig  temperature 6dev.flutterberlin.flutter_gemma.InferenceSessionConfig  topK 6dev.flutterberlin.flutter_gemma.InferenceSessionConfig  topP 6dev.flutterberlin.flutter_gemma.InferenceSessionConfig  PreferredBackend :dev.flutterberlin.flutter_gemma.PigeonInterfacePigeonCodec  let :dev.flutterberlin.flutter_gemma.PigeonInterfacePigeonCodec  ofRaw :dev.flutterberlin.flutter_gemma.PigeonInterfacePigeonCodec  	readValue :dev.flutterberlin.flutter_gemma.PigeonInterfacePigeonCodec  
writeValue :dev.flutterberlin.flutter_gemma.PigeonInterfacePigeonCodec  Any /dev.flutterberlin.flutter_gemma.PlatformService  BasicMessageChannel /dev.flutterberlin.flutter_gemma.PlatformService  BinaryMessenger /dev.flutterberlin.flutter_gemma.PlatformService  Boolean /dev.flutterberlin.flutter_gemma.PlatformService  	ByteArray /dev.flutterberlin.flutter_gemma.PlatformService  	Companion /dev.flutterberlin.flutter_gemma.PlatformService  Double /dev.flutterberlin.flutter_gemma.PlatformService  JvmOverloads /dev.flutterberlin.flutter_gemma.PlatformService  List /dev.flutterberlin.flutter_gemma.PlatformService  Long /dev.flutterberlin.flutter_gemma.PlatformService  MessageCodec /dev.flutterberlin.flutter_gemma.PlatformService  PigeonInterfacePigeonCodec /dev.flutterberlin.flutter_gemma.PlatformService  PlatformService /dev.flutterberlin.flutter_gemma.PlatformService  PreferredBackend /dev.flutterberlin.flutter_gemma.PlatformService  Result /dev.flutterberlin.flutter_gemma.PlatformService  String /dev.flutterberlin.flutter_gemma.PlatformService  Unit /dev.flutterberlin.flutter_gemma.PlatformService  addImage /dev.flutterberlin.flutter_gemma.PlatformService  
addQueryChunk /dev.flutterberlin.flutter_gemma.PlatformService  
closeModel /dev.flutterberlin.flutter_gemma.PlatformService  closeSession /dev.flutterberlin.flutter_gemma.PlatformService  codec /dev.flutterberlin.flutter_gemma.PlatformService  createModel /dev.flutterberlin.flutter_gemma.PlatformService  
createSession /dev.flutterberlin.flutter_gemma.PlatformService  generateResponse /dev.flutterberlin.flutter_gemma.PlatformService  generateResponseAsync /dev.flutterberlin.flutter_gemma.PlatformService  getValue /dev.flutterberlin.flutter_gemma.PlatformService  
isNotEmpty /dev.flutterberlin.flutter_gemma.PlatformService  lazy /dev.flutterberlin.flutter_gemma.PlatformService  provideDelegate /dev.flutterberlin.flutter_gemma.PlatformService  run /dev.flutterberlin.flutter_gemma.PlatformService  setUp /dev.flutterberlin.flutter_gemma.PlatformService  sizeInTokens /dev.flutterberlin.flutter_gemma.PlatformService  	wrapError /dev.flutterberlin.flutter_gemma.PlatformService  
wrapResult /dev.flutterberlin.flutter_gemma.PlatformService  BasicMessageChannel 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  PigeonInterfacePigeonCodec 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  codec 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  getValue 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  
isNotEmpty 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  lazy 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  provideDelegate 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  run 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  setUp 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  	wrapError 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  
wrapResult 9dev.flutterberlin.flutter_gemma.PlatformService.Companion  CoroutineScope 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  Dispatchers 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  IllegalStateException 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  InferenceModel 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  InferenceModelConfig 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  InferenceSessionConfig 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  PreferredBackendEnum 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  Result 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  Unit 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  context 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  	eventSink 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  failure 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  inferenceModel 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  launch 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  let 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  map 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  mapOf 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  scope 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  session 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  success 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  to 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  withContext 3dev.flutterberlin.flutter_gemma.PlatformServiceImpl  	Companion 0dev.flutterberlin.flutter_gemma.PreferredBackend  Int 0dev.flutterberlin.flutter_gemma.PreferredBackend  PreferredBackend 0dev.flutterberlin.flutter_gemma.PreferredBackend  firstOrNull 0dev.flutterberlin.flutter_gemma.PreferredBackend  let 0dev.flutterberlin.flutter_gemma.PreferredBackend  ofRaw 0dev.flutterberlin.flutter_gemma.PreferredBackend  ordinal 0dev.flutterberlin.flutter_gemma.PreferredBackend  raw 0dev.flutterberlin.flutter_gemma.PreferredBackend  values 0dev.flutterberlin.flutter_gemma.PreferredBackend  firstOrNull :dev.flutterberlin.flutter_gemma.PreferredBackend.Companion  ofRaw :dev.flutterberlin.flutter_gemma.PreferredBackend.Companion  values :dev.flutterberlin.flutter_gemma.PreferredBackend.Companion  let 4dev.flutterberlin.flutter_gemma.PreferredBackendEnum  ordinal 4dev.flutterberlin.flutter_gemma.PreferredBackendEnum  values 4dev.flutterberlin.flutter_gemma.PreferredBackendEnum  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BasicMessageChannel io.flutter.plugin.common  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  MessageCodec io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  StandardMethodCodec io.flutter.plugin.common  MessageHandler ,io.flutter.plugin.common.BasicMessageChannel  Reply ,io.flutter.plugin.common.BasicMessageChannel  setMessageHandler ,io.flutter.plugin.common.BasicMessageChannel  <SAM-CONSTRUCTOR> ;io.flutter.plugin.common.BasicMessageChannel.MessageHandler  reply 2io.flutter.plugin.common.BasicMessageChannel.Reply  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  endOfStream /io.flutter.plugin.common.EventChannel.EventSink  error /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink  Long -io.flutter.plugin.common.StandardMessageCodec  PreferredBackend -io.flutter.plugin.common.StandardMessageCodec  let -io.flutter.plugin.common.StandardMessageCodec  ofRaw -io.flutter.plugin.common.StandardMessageCodec  	readValue -io.flutter.plugin.common.StandardMessageCodec  readValueOfType -io.flutter.plugin.common.StandardMessageCodec  
writeValue -io.flutter.plugin.common.StandardMessageCodec  ByteArrayOutputStream java.io  File java.io  write java.io.ByteArrayOutputStream  exists java.io.File  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  RuntimeException 	java.lang  
simpleName java.lang.Class  message java.lang.Exception  
ByteBuffer java.nio  Array kotlin  	ByteArray kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  apply kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  run kotlin  to kotlin  toString 
kotlin.Any  firstOrNull kotlin.Array  get kotlin.Array  	getOrNull kotlin.Array  let kotlin.Boolean  not kotlin.Boolean  size kotlin.ByteArray  toFloat 
kotlin.Double  	Companion kotlin.Enum  Int kotlin.Enum  PreferredBackend kotlin.Enum  firstOrNull kotlin.Enum  values kotlin.Enum  firstOrNull kotlin.Enum.Companion  values kotlin.Enum.Companion  let kotlin.Float  invoke kotlin.Function1  let 
kotlin.Int  toByte 
kotlin.Int  toLong 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  let kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.Result  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	getOrNull 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  
isNotEmpty 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  to 
kotlin.String  cause kotlin.Throwable  	javaClass kotlin.Throwable  message kotlin.Throwable  toString kotlin.Throwable  List kotlin.collections  Map kotlin.collections  firstOrNull kotlin.collections  	getOrNull kotlin.collections  getValue kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  get kotlin.collections.List  let kotlin.collections.List  map kotlin.collections.List  SuspendFunction1 kotlin.coroutines  JvmOverloads 
kotlin.jvm  	javaClass 
kotlin.jvm  firstOrNull 
kotlin.ranges  KClass kotlin.reflect  
KProperty1 kotlin.reflect  Sequence kotlin.sequences  firstOrNull kotlin.sequences  map kotlin.sequences  firstOrNull kotlin.text  	getOrNull kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  Any kotlinx.coroutines  
BitmapFactory kotlinx.coroutines  BitmapImageBuilder kotlinx.coroutines  Boolean kotlinx.coroutines  BufferOverflow kotlinx.coroutines  	ByteArray kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Double kotlinx.coroutines  EventChannel kotlinx.coroutines  	Exception kotlinx.coroutines  File kotlinx.coroutines  Float kotlinx.coroutines  
FlutterPlugin kotlinx.coroutines  GraphOptions kotlinx.coroutines  IllegalArgumentException kotlinx.coroutines  IllegalStateException kotlinx.coroutines  InferenceModel kotlinx.coroutines  InferenceModelConfig kotlinx.coroutines  InferenceModelSession kotlinx.coroutines  InferenceSessionConfig kotlinx.coroutines  Int kotlinx.coroutines  Job kotlinx.coroutines  List kotlinx.coroutines  LlmInference kotlinx.coroutines  LlmInferenceSession kotlinx.coroutines  Long kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  MutableSharedFlow kotlinx.coroutines  Pair kotlinx.coroutines  PlatformService kotlinx.coroutines  PlatformServiceImpl kotlinx.coroutines  PreferredBackend kotlinx.coroutines  PreferredBackendEnum kotlinx.coroutines  Result kotlinx.coroutines  RuntimeException kotlinx.coroutines  
SharedFlow kotlinx.coroutines  String kotlinx.coroutines  	Throwable kotlinx.coroutines  Unit kotlinx.coroutines  apply kotlinx.coroutines  asSharedFlow kotlinx.coroutines  config kotlinx.coroutines  context kotlinx.coroutines  failure kotlinx.coroutines  	getOrNull kotlinx.coroutines  inferenceModel kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  map kotlinx.coroutines  mapOf kotlinx.coroutines  session kotlinx.coroutines  setUp kotlinx.coroutines  success kotlinx.coroutines  to kotlinx.coroutines  withContext kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  IllegalStateException !kotlinx.coroutines.CoroutineScope  InferenceModel !kotlinx.coroutines.CoroutineScope  InferenceModelConfig !kotlinx.coroutines.CoroutineScope  InferenceSessionConfig !kotlinx.coroutines.CoroutineScope  PreferredBackendEnum !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  Unit !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  failure !kotlinx.coroutines.CoroutineScope  inferenceModel !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  mapOf !kotlinx.coroutines.CoroutineScope  session !kotlinx.coroutines.CoroutineScope  success !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  	EventSink kotlinx.coroutines.EventChannel  
StreamHandler kotlinx.coroutines.EventChannel  FlutterPluginBinding  kotlinx.coroutines.FlutterPlugin  BufferOverflow kotlinx.coroutines.channels  DROP_OLDEST *kotlinx.coroutines.channels.BufferOverflow  
FlowCollector kotlinx.coroutines.flow  MutableSharedFlow kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  asSharedFlow kotlinx.coroutines.flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asSharedFlow )kotlinx.coroutines.flow.MutableSharedFlow  tryEmit )kotlinx.coroutines.flow.MutableSharedFlow  collect "kotlinx.coroutines.flow.SharedFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             