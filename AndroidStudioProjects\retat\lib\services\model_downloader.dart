import 'dart:io';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:permission_handler/permission_handler.dart';

class ModelDownloader {
  // Model download URL. Using a smaller alternative model that doesn't require authentication
  static const String modelUrl =
      'https://huggingface.co/litert-community/Gemma3-1B-IT/resolve/main/Gemma3-1B-IT_multi-prefill-seq_q4_ekv2048.task';
  static const String modelFileName =
      'Gemma3-1B-IT_multi-prefill-seq_q4_ekv2048.task';

  // Alternative: Original Gemma 3n model (requires Hugging Face authentication)
  static const String originalModelUrl =
      'https://huggingface.co/google/gemma-3n-E2B-it-litert-preview/resolve/main/gemma-3n-E2B-it-int4.task';
  static const String originalModelFileName = 'gemma-3n-E2B-it-int4.task';

  /// Checks if internet connection is available
  static Future<bool> hasInternetConnection() async {
    try {
      print('[ModelDownloader] Checking internet connection...');
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 5);
      dio.options.receiveTimeout = const Duration(seconds: 5);

      // Try to reach a simple endpoint
      final response = await dio.get('https://www.google.com');
      final hasConnection = response.statusCode == 200;
      print('[ModelDownloader] Internet connection: $hasConnection');
      return hasConnection;
    } catch (e) {
      print('[ModelDownloader] No internet connection: $e');
      return false;
    }
  }

  /// Returns the local file path where the model should be stored.
  static Future<String> getModelFilePath() async {
    final dir = await getApplicationDocumentsDirectory();
    return p.join(dir.path, modelFileName);
  }

  /// Checks if the model file exists locally and matches the expected size.
  /// Returns true if model is complete, false if it needs to be downloaded.
  static Future<bool> modelIsComplete() async {
    print('[ModelDownloader] Checking if model is complete...');

    final path = await getModelFilePath();
    final file = File(path);

    print('[ModelDownloader] Model path: $path');

    if (await file.exists()) {
      print('[ModelDownloader] Model file exists locally, checking size...');
      // File exists in app's directory, no permission needed
      try {
        final expectedSize = await getRemoteFileSize();
        final localSize = await file.length();
        print(
            '[ModelDownloader] Local model size: $localSize, Expected size: $expectedSize');

        if (expectedSize == null) {
          print(
              '[ModelDownloader] Could not get expected size from server. Assuming model is complete.');
          return true;
        }

        final isComplete = localSize == expectedSize;
        print('[ModelDownloader] Model is complete: $isComplete');
        return isComplete;
      } catch (e) {
        print('[ModelDownloader] Error checking model size: $e');
        // If we can't check size, assume it's incomplete and needs re-download
        return false;
      }
    }

    print(
        '[ModelDownloader] Model file does not exist in app directory, checking other locations...');

    // Try to copy from app external files or Download if available
    final copied = await _tryCopyFromSdcardOrDownload(path);
    if (!copied) {
      print(
          '[ModelDownloader] Model file does not exist and could not be copied. Download needed.');
      return false;
    }

    print(
        '[ModelDownloader] Model copied from external location, verifying...');

    // After copying, check size again
    try {
      final expectedSize = await getRemoteFileSize();
      final localSize = await file.length();
      print(
          '[ModelDownloader] Copied model size: $localSize, Expected size: $expectedSize');

      if (expectedSize == null) {
        print(
            '[ModelDownloader] Could not get expected size from server. Assuming copied model is complete.');
        return true;
      }

      final isComplete = localSize == expectedSize;
      print('[ModelDownloader] Copied model is complete: $isComplete');
      return isComplete;
    } catch (e) {
      print('[ModelDownloader] Error verifying copied model: $e');
      return false;
    }
  }

  /// Tries to copy the model from /sdcard/Android/data or /sdcard/Download to app storage if present.
  static Future<bool> _tryCopyFromSdcardOrDownload(String destPath) async {
    final candidates = [
      '/storage/emulated/0/Android/data/com.example.myapp/files/gemma-3n-E2B-it-int4.task',
      '/sdcard/Android/data/com.example.myapp/files/gemma-3n-E2B-it-int4.task',
      '/sdcard/Download/gemma-3n-E2B-it-int4.task',
    ];
    for (final candidate in candidates) {
      final file = File(candidate);
      if (await file.exists()) {
        print(
            '[ModelDownloader] Found model at $candidate, attempting to copy to $destPath');
        // Only request permission if accessing Downloads
        if (candidate.contains('/Download/')) {
          final status = await Permission.storage.request();
          if (!status.isGranted) {
            print(
                '[ModelDownloader] Storage permission not granted. Cannot copy model file from Downloads.');
            continue;
          }
        }
        try {
          await file.copy(destPath);
          print('[ModelDownloader] Copy succeeded from $candidate');
          return true;
        } catch (e) {
          print('[ModelDownloader] Copy failed from $candidate: $e');
        }
      } else {
        print('[ModelDownloader] Model not found at $candidate');
      }
    }
    return false;
  }

  /// Gets the expected file size from the server using a HEAD request.
  static Future<int?> getRemoteFileSize() async {
    try {
      print('[ModelDownloader] Getting remote file size...');
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 10);
      dio.options.receiveTimeout = const Duration(seconds: 10);

      final response = await dio.head(modelUrl);
      final contentLength = response.headers.value('content-length');

      if (contentLength != null) {
        final size = int.tryParse(contentLength);
        print('[ModelDownloader] Remote file size: $size bytes');
        return size;
      } else {
        print('[ModelDownloader] No content-length header found');
        return null;
      }
    } catch (e) {
      print('[ModelDownloader] Error getting remote file size: $e');
      return null;
    }
  }

  /// Downloads the model file, reporting progress (0.0 to 1.0) via [onProgress].
  /// If a partial file exists, resumes download if possible.
  static Future<void> downloadModel(
      {required void Function(double) onProgress}) async {
    print('[ModelDownloader] Starting model download...');

    try {
      // Request storage permission
      final storageStatus = await Permission.storage.request();
      print('[ModelDownloader] Storage permission status: $storageStatus');

      final path = await getModelFilePath();
      final file = File(path);
      print('[ModelDownloader] Download path: $path');

      // Create directory if it doesn't exist
      final directory = file.parent;
      if (!await directory.exists()) {
        await directory.create(recursive: true);
        print('[ModelDownloader] Created directory: ${directory.path}');
      }

      final dio = Dio();
      dio.options.connectTimeout = const Duration(minutes: 5);
      dio.options.receiveTimeout = const Duration(minutes: 30);

      int? expectedSize = await getRemoteFileSize();
      int downloaded = 0;

      if (await file.exists()) {
        downloaded = await file.length();
        print('[ModelDownloader] Existing file size: $downloaded bytes');

        // If file is complete, skip download
        if (expectedSize != null && downloaded == expectedSize) {
          print('[ModelDownloader] File already complete, skipping download');
          onProgress(1.0);
          return;
        }
      }

      print('[ModelDownloader] Starting download from byte: $downloaded');

      await dio.download(
        modelUrl,
        path,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            print(
                '[ModelDownloader] Progress: ${(progress * 100).toStringAsFixed(1)}%');
            onProgress(progress);
          } else {
            // If total is unknown, calculate based on expected size
            if (expectedSize != null) {
              final progress = (downloaded + received) / expectedSize;
              onProgress(progress.clamp(0.0, 1.0));
            }
          }
        },
        deleteOnError: true,
        options: Options(
          headers: downloaded > 0 ? {'range': 'bytes=$downloaded-'} : null,
        ),
      );

      print('[ModelDownloader] Download completed');

      // Verify file size after download
      final finalSize = await file.length();
      print('[ModelDownloader] Final file size: $finalSize bytes');

      if (expectedSize != null && finalSize != expectedSize) {
        print(
            '[ModelDownloader] File size mismatch. Expected: $expectedSize, Got: $finalSize');
        if (await file.exists()) {
          await file.delete();
        }
        throw Exception(
            'Downloaded file is incomplete or corrupt. Expected: $expectedSize bytes, Got: $finalSize bytes');
      }

      print('[ModelDownloader] Download verification successful');
    } catch (e) {
      print('[ModelDownloader] Download error: $e');

      // Clean up partial file on error
      final path = await getModelFilePath();
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        print('[ModelDownloader] Cleaned up partial file');
      }

      rethrow;
    }
  }
}
